version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: karmsakha-postgres
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_DB: ${POSTGRES_DB:-karmsakha}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres}"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: karmsakha-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  api_gateway:
    build:
      context: .
      dockerfile: Dockerfile.python
    container_name: karmsakha-api
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-karmsakha}
      - POSTGRES_PORT=5432
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - MODEL_API_KEY=${OPENAI_API_KEY}  # Stagehand expects MODEL_API_KEY
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - BROWSERBASE_API_KEY=${BROWSERBASE_API_KEY}
      - BROWSERBASE_PROJECT_ID=${BROWSERBASE_PROJECT_ID}
      - ZOHO_IMAP_HOST=${ZOHO_IMAP_HOST}
      - ZOHO_IMAP_PORT=${ZOHO_IMAP_PORT}
      - ZOHO_IMAP_USER=${ZOHO_IMAP_USER}
      - ZOHO_IMAP_PASS=${ZOHO_IMAP_PASS}
      - ZOHO_SMTP_HOST=${ZOHO_SMTP_HOST}
      - ZOHO_SMTP_PORT=${ZOHO_SMTP_PORT}
      - ZOHO_SMTP_USER=${ZOHO_SMTP_USER}
      - ZOHO_SMTP_PASS=${ZOHO_SMTP_PASS}
      - EMAIL_DOMAIN=${EMAIL_DOMAIN}
      - PYTHONPATH=/app
    ports:
      - "8000:8000"
    volumes:
      - ./data:/data
      - ./logs:/logs
    command: python -m uvicorn api_gateway.main:app --host 0.0.0.0 --port 8000 --reload
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3

  seek_worker:
    build:
      context: .
      dockerfile: Dockerfile.python
    container_name: karmsakha-worker
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      stagehand_service:
        condition: service_healthy
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-karmsakha}
      - POSTGRES_PORT=5432
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - MODEL_API_KEY=${OPENAI_API_KEY}  # Stagehand expects MODEL_API_KEY
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - BROWSERBASE_API_KEY=${BROWSERBASE_API_KEY}
      - BROWSERBASE_PROJECT_ID=${BROWSERBASE_PROJECT_ID}
      - ZOHO_IMAP_HOST=${ZOHO_IMAP_HOST}
      - ZOHO_IMAP_PORT=${ZOHO_IMAP_PORT}
      - ZOHO_IMAP_USER=${ZOHO_IMAP_USER}
      - ZOHO_IMAP_PASS=${ZOHO_IMAP_PASS}
      - ZOHO_SMTP_HOST=${ZOHO_SMTP_HOST}
      - ZOHO_SMTP_PORT=${ZOHO_SMTP_PORT}
      - ZOHO_SMTP_USER=${ZOHO_SMTP_USER}
      - ZOHO_SMTP_PASS=${ZOHO_SMTP_PASS}
      - EMAIL_DOMAIN=${EMAIL_DOMAIN}
      - PYTHONPATH=/app
      - DISPLAY=:99
      - SEARXNG_BASE_URL=https://search.sapti.me
      - STAGEHAND_SERVICE_URL=http://stagehand_service:3001
    volumes:
      - ./data:/data
      - ./logs:/logs
    command: python workers/apply_worker.py
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
    cap_add:
      - SYS_ADMIN  # Required for Chrome/Chromium

  stagehand_service:
    build:
      context: .
      dockerfile: Dockerfile.stagehand
    container_name: karmsakha-stagehand
    environment:
      - BROWSERBASE_API_KEY=${BROWSERBASE_API_KEY}
      - BROWSERBASE_PROJECT_ID=${BROWSERBASE_PROJECT_ID}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - PORT=3001
    volumes:
      - ./data:/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

volumes:
  postgres_data:
  redis_data:

networks:
  default:
    name: karmsakha-network