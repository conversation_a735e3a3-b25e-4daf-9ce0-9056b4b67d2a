"""
FastAPI Gateway for Karmsakha Autopilot
Provides endpoints for job search and application status tracking
"""
import json
import logging
import os
import sys
from datetime import datetime
from typing import Dict, Any, List, Optional

import redis
from fastapi import FastAPI, HTTPException, Query
from fastapi.responses import FileResponse
from pydantic import BaseModel, HttpUrl
from sqlalchemy import create_engine, Column, String, Integer, DateTime, Text, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session as DBSession
from dotenv import load_dotenv

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import JobSpy - handle different import paths
try:
    from jobspy import scrape_jobs
    from jobspy.proxy_manager import create_proxy_manager
except ImportError:
    try:
        from src.jobspy.jobspy import scrape_jobs
        # Mock proxy manager if not available
        def create_proxy_manager(config):
            return None
    except ImportError:
        # Fallback - create mock functions if JobSpy not available
        def scrape_jobs(**kwargs):
            return []
        def create_proxy_manager(config):
            return None

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Karmsakha Autopilot API",
    description="Job application automation pipeline",
    version="1.0.0"
)

# Database models
Base = declarative_base()

class JobRaw(Base):
    __tablename__ = 'jobs_raw'
    
    id = Column(Integer, primary_key=True)
    job_id = Column(String, unique=True, index=True)
    url = Column(String)
    company = Column(String)
    title = Column(String)
    location = Column(String)
    description = Column(Text)
    salary = Column(String)
    job_type = Column(String)
    posted_date = Column(String)
    source = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
    processed = Column(Boolean, default=False)

class Application(Base):
    __tablename__ = 'applications'
    
    id = Column(Integer, primary_key=True)
    job_id = Column(String, index=True)
    job_url = Column(String)
    company = Column(String)
    title = Column(String)
    email_alias = Column(String)
    status = Column(String, default='pending')
    screenshot_path = Column(String)
    error_message = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    verification_completed = Column(Boolean, default=False)

# Initialize connections
redis_client = redis.Redis(
    host=os.getenv('REDIS_HOST', 'redis'),
    port=int(os.getenv('REDIS_PORT', 6379)),
    decode_responses=True
)

# Database connection
DATABASE_URL = os.getenv(
    'DATABASE_URL',
    f"postgresql://{os.getenv('POSTGRES_USER', 'postgres')}:"
    f"{os.getenv('POSTGRES_PASSWORD', 'postgres')}@"
    f"{os.getenv('POSTGRES_HOST', 'postgres')}:"
    f"{os.getenv('POSTGRES_PORT', 5432)}/"
    f"{os.getenv('POSTGRES_DB', 'karmsakha')}"
)

engine = create_engine(DATABASE_URL)
Base.metadata.create_all(engine)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Pydantic models
class JobSearchRequest(BaseModel):
    query: str
    location: Optional[str] = None
    site: Optional[str] = "google"  # Changed default to Google Jobs (aggregates multiple sources)
    results_wanted: Optional[int] = 10
    hours_old: Optional[int] = 72
    country: Optional[str] = "India"
    use_proxies: Optional[bool] = False  # Enable proxy usage

class JobSearchResponse(BaseModel):
    jobs_found: int
    jobs_queued: int
    message: str

class ApplicationStatus(BaseModel):
    job_id: str
    status: str
    company: Optional[str] = None
    title: Optional[str] = None
    email_alias: Optional[str] = None
    screenshot_url: Optional[str] = None
    error_message: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

# Dependency to get DB session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "status": "active",
        "service": "Karmsakha Autopilot API",
        "version": "1.0.0"
    }

@app.post("/search", response_model=JobSearchResponse)
async def search_jobs(request: JobSearchRequest):
    """
    Search for jobs using JobSpy and queue them for processing
    """
    db = next(get_db())

    try:
        logger.info(f"Searching for jobs: {request.query} in {request.location}")

        # Use JobSpy to search for jobs - support multiple sites
        sites_to_search = [request.site] if request.site else ["google", "glassdoor", "zip_recruiter", "bayt"]

        # If user specifically requests multiple sites (comma-separated)
        if request.site and "," in request.site:
            sites_to_search = [site.strip().lower() for site in request.site.split(",")]

        # Setup proxies if requested
        proxies = None
        if request.use_proxies:
            # For demo purposes, using free proxies. In production, use paid proxies
            proxy_config = {
                'type': 'free',
                'test_on_init': True
            }
            proxy_manager = create_proxy_manager(proxy_config)
            if proxy_manager:
                working_proxy = proxy_manager.get_working_proxy()
                if working_proxy:
                    proxies = [working_proxy.url]
                    logger.info(f"Using proxy: {working_proxy.url}")

        jobs = scrape_jobs(
            site_name=sites_to_search,
            search_term=request.query,
            location=request.location,
            results_wanted=request.results_wanted,
            hours_old=request.hours_old,
            country_indeed=request.country,
            proxies=proxies
        )

        if jobs is None or (hasattr(jobs, 'empty') and jobs.empty):
            return JobSearchResponse(
                jobs_found=0,
                jobs_queued=0,
                message="No jobs found matching the criteria"
            )

        # Convert to list of dictionaries if it's a DataFrame
        if hasattr(jobs, 'to_dict'):
            jobs_list = jobs.to_dict('records')
        else:
            jobs_list = jobs if isinstance(jobs, list) else []

        jobs_queued = 0

        for job in jobs_list:
            # Create unique job ID
            job_id = f"{request.site}_{job.get('id', '')}_{hash(job.get('job_url', ''))}"

            # Check if job already exists
            existing_job = db.query(JobRaw).filter_by(job_id=job_id).first()

            if not existing_job:
                # Store in database
                job_raw = JobRaw(
                    job_id=job_id,
                    url=job.get('job_url', ''),
                    company=job.get('company', ''),
                    title=job.get('title', ''),
                    location=job.get('location', ''),
                    description=job.get('description', ''),
                    salary=str(job.get('min_amount', '')) or str(job.get('max_amount', '')) or '',
                    job_type=str(job.get('job_type', '')) if job.get('job_type') is not None else '',
                    posted_date=str(job.get('date_posted', '')),
                    source=request.site
                )
                db.add(job_raw)

                # Queue for processing
                job_data = {
                    'id': job_id,
                    'url': job.get('job_url', ''),
                    'company': job.get('company', ''),
                    'title': job.get('title', ''),
                    'location': job.get('location', ''),
                    'source': request.site
                }

                redis_client.rpush('apply_queue', json.dumps(job_data))
                jobs_queued += 1

                logger.info(f"Queued job: {job_data['company']} - {job_data['title']}")

        db.commit()

        return JobSearchResponse(
            jobs_found=len(jobs_list),
            jobs_queued=jobs_queued,
            message=f"Successfully queued {jobs_queued} new jobs for processing"
        )

    except Exception as e:
        logger.error(f"Error in job search: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        db.close()

@app.post("/search-profile", response_model=JobSearchResponse)
async def search_jobs_from_profile(
    location: Optional[str] = None,
    site: Optional[str] = "google",  # Changed default to Google Jobs
    results_wanted: Optional[int] = 10,
    hours_old: Optional[int] = 72
):
    """
    Search for jobs based on user's profile preferences and roles of interest
    """
    db = next(get_db())

    try:
        # Load profile to get roles of interest
        import json
        with open('/app/profile.json', 'r') as f:
            profile_data = json.load(f)

        roles_of_interest = profile_data.get('preferences', {}).get('roles_of_interest', [])
        if not roles_of_interest:
            return JobSearchResponse(
                jobs_found=0,
                jobs_queued=0,
                message="No roles of interest found in profile"
            )

        # Use profile location if not specified
        if not location:
            location = profile_data.get('personal_info', {}).get('address', {}).get('city', 'New Delhi')

        all_jobs = []
        total_queued = 0

        # Search for each role of interest
        for role in roles_of_interest:
            logger.info(f"Searching for jobs: {role} in {location}")

            jobs = scrape_jobs(
                site_name=[site],
                search_term=role,
                location=location,
                results_wanted=results_wanted // len(roles_of_interest) + 1,  # Distribute results across roles
                hours_old=hours_old,
                country_indeed="India"
            )

            if jobs is not None and not (hasattr(jobs, 'empty') and jobs.empty):
                # Convert to list of dictionaries if it's a DataFrame
                if hasattr(jobs, 'to_dict'):
                    jobs_list = jobs.to_dict('records')
                else:
                    jobs_list = jobs if isinstance(jobs, list) else []

                all_jobs.extend(jobs_list)

        if not all_jobs:
            return JobSearchResponse(
                jobs_found=0,
                jobs_queued=0,
                message="No jobs found matching profile preferences"
            )

        # Queue jobs for processing (similar to original search endpoint)
        for job in all_jobs:
            # Create unique job ID
            job_id = f"{site}_{job.get('id', '')}_{hash(job.get('job_url', ''))}"

            # Check if job already exists in database
            existing_job = db.query(JobRaw).filter_by(job_id=job_id).first()

            if existing_job:
                logger.info(f"Job already exists: {job.get('company')} - {job.get('title')}")
                continue

            # Create new job record
            new_job = JobRaw(
                job_id=job_id,
                url=job.get('job_url', ''),
                company=job.get('company', ''),
                title=job.get('title', ''),
                location=job.get('location', ''),
                description=job.get('description', ''),
                salary=str(job.get('min_amount', '')) or str(job.get('max_amount', '')) or '',
                job_type=str(job.get('job_type', '')) if job.get('job_type') is not None else '',
                posted_date=str(job.get('date_posted', '')),
                source=site
            )
            db.add(new_job)

            # Prepare job data for Redis queue
            job_data = {
                'id': job_id,
                'url': job.get('job_url', ''),
                'company': job.get('company', ''),
                'title': job.get('title', ''),
                'location': job.get('location', ''),
                'source': site
            }

            redis_client.rpush('apply_queue', json.dumps(job_data))
            total_queued += 1

            logger.info(f"Queued job: {job_data['company']} - {job_data['title']}")

        db.commit()

        return JobSearchResponse(
            jobs_found=len(all_jobs),
            jobs_queued=total_queued,
            message=f"Successfully queued {total_queued} relevant jobs based on profile preferences"
        )

    except Exception as e:
        logger.error(f"Error in profile-based job search: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        db.close()

@app.get("/status/{job_id}", response_model=ApplicationStatus)
async def get_application_status(job_id: str):
    """
    Get the status of a job application
    """
    db = next(get_db())
    
    try:
        # First check Redis for recent status
        redis_status = redis_client.get(f"application_status:{job_id}")
        
        # Then check database
        application = db.query(Application).filter_by(job_id=job_id).first()
        
        if not application and not redis_status:
            raise HTTPException(status_code=404, detail="Application not found")
        
        # Build response
        if application:
            response = ApplicationStatus(
                job_id=application.job_id,
                status=application.status,
                company=application.company,
                title=application.title,
                email_alias=application.email_alias,
                screenshot_url=f"/screenshots/{os.path.basename(application.screenshot_path)}" if application.screenshot_path else None,
                error_message=application.error_message,
                created_at=application.created_at,
                updated_at=application.updated_at
            )
        else:
            # Parse Redis status
            status_data = json.loads(redis_status)
            response = ApplicationStatus(
                job_id=job_id,
                status=status_data.get('status', 'unknown'),
                error_message=status_data.get('error')
            )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting application status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        db.close()

@app.get("/screenshots/{filename}")
async def get_screenshot(filename: str):
    """
    Serve screenshot files
    """
    screenshot_path = f"/data/screens/{filename}"
    
    if not os.path.exists(screenshot_path):
        raise HTTPException(status_code=404, detail="Screenshot not found")
    
    return FileResponse(screenshot_path, media_type="image/png")

@app.get("/jobs")
async def list_jobs(
    status: Optional[str] = Query(None, description="Filter by processing status"),
    limit: int = Query(50, description="Maximum number of results"),
    offset: int = Query(0, description="Number of results to skip")
):
    """
    List all jobs in the system
    """
    db = next(get_db())
    
    try:
        query = db.query(JobRaw)
        
        if status == "processed":
            query = query.filter_by(processed=True)
        elif status == "pending":
            query = query.filter_by(processed=False)
        
        total = query.count()
        jobs = query.offset(offset).limit(limit).all()
        
        return {
            "total": total,
            "limit": limit,
            "offset": offset,
            "jobs": [
                {
                    "job_id": job.job_id,
                    "company": job.company,
                    "title": job.title,
                    "location": job.location,
                    "url": job.url,
                    "source": job.source,
                    "created_at": job.created_at,
                    "processed": job.processed
                }
                for job in jobs
            ]
        }
        
    except Exception as e:
        logger.error(f"Error listing jobs: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        db.close()

@app.get("/applications")
async def list_applications(
    status: Optional[str] = Query(None, description="Filter by application status"),
    limit: int = Query(50, description="Maximum number of results"),
    offset: int = Query(0, description="Number of results to skip")
):
    """
    List all applications in the system
    """
    db = next(get_db())
    
    try:
        query = db.query(Application)
        
        if status:
            query = query.filter_by(status=status)
        
        total = query.count()
        applications = query.offset(offset).limit(limit).all()
        
        return {
            "total": total,
            "limit": limit,
            "offset": offset,
            "applications": [
                {
                    "id": app.id,
                    "job_id": app.job_id,
                    "company": app.company,
                    "title": app.title,
                    "status": app.status,
                    "email_alias": app.email_alias,
                    "verification_completed": app.verification_completed,
                    "created_at": app.created_at,
                    "updated_at": app.updated_at,
                    "screenshot_url": f"/screenshots/{os.path.basename(app.screenshot_path)}" if app.screenshot_path else None
                }
                for app in applications
            ]
        }
        
    except Exception as e:
        logger.error(f"Error listing applications: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        db.close()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)