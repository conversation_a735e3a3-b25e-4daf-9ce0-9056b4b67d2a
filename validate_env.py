#!/usr/bin/env python3
"""
Environment Variable Validation Script
Ensures all required environment variables are configured before starting services.
"""

import os
import sys
from typing import List, Dict

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("📄 Loaded variables from .env file")
except ImportError:
    print("⚠️  python-dotenv not available, using system environment only")

def validate_environment() -> Dict[str, List[str]]:
    """
    Validate all required environment variables.
    
    Returns:
        Dictionary with 'missing' and 'configured' keys
    """
    
    # Critical variables required for core functionality
    required_vars = [
        'OPENAI_API_KEY',
        'BROWSERBASE_API_KEY',
        'BROWSERBASE_PROJECT_ID',
        'POSTGRES_USER',
        'POSTGRES_PASSWORD',
        'POSTGRES_DB',
        'POSTGRES_HOST',
        'REDIS_HOST'
    ]
    
    # Optional but recommended variables
    optional_vars = [
        'ZOHO_IMAP_USER',
        'ZOHO_IMAP_PASS',
        'EMAIL_DOMAIN',
        'STAGEHAND_SERVICE_URL',
        'OPENROUTER_API_KEY'
    ]
    
    missing_required = []
    missing_optional = []
    configured = []
    
    # Check required variables
    for var in required_vars:
        if not os.getenv(var):
            missing_required.append(var)
        else:
            configured.append(var)
    
    # Check optional variables
    for var in optional_vars:
        if not os.getenv(var):
            missing_optional.append(var)
        else:
            configured.append(var)
    
    return {
        'missing_required': missing_required,
        'missing_optional': missing_optional,
        'configured': configured
    }

def main():
    """Main validation function"""
    print("🔍 Validating environment variables...")
    
    result = validate_environment()
    
    # Print configured variables
    if result['configured']:
        print(f"\n✅ Configured variables ({len(result['configured'])}):")
        for var in sorted(result['configured']):
            value = os.getenv(var, '')
            # Mask sensitive values
            if 'API_KEY' in var or 'PASSWORD' in var or 'PASS' in var:
                display_value = f"{value[:8]}***" if len(value) > 8 else "***"
            else:
                display_value = value
            print(f"   {var}={display_value}")
    
    # Print missing optional variables
    if result['missing_optional']:
        print(f"\n⚠️  Missing optional variables ({len(result['missing_optional'])}):")
        for var in sorted(result['missing_optional']):
            print(f"   {var}")
        print("   These are optional but recommended for full functionality.")
    
    # Print missing required variables
    if result['missing_required']:
        print(f"\n❌ Missing required variables ({len(result['missing_required'])}):")
        for var in sorted(result['missing_required']):
            print(f"   {var}")
        print("\n💡 Please configure these variables before starting the services.")
        print("   Copy .env.example to .env and fill in the values.")
        return 1
    
    print(f"\n🎉 Environment validation successful!")
    print(f"   {len(result['configured'])} variables configured")
    print(f"   {len(result['missing_optional'])} optional variables missing")
    print(f"   Ready to start services!")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())