#!/usr/bin/env python3
"""
Stagehand Flow - Real Job Application Demo
Uses the Stagehand service for autonomous job applications
"""

import asyncio
import sys
import os
import json
import httpx
from datetime import datetime

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

async def apply_to_job_with_stagehand(job_data, application_data):
    """
    Apply to a job using the Stagehand service
    """
    
    stagehand_url = "http://localhost:3001/apply"
    
    payload = {
        "jobData": job_data,
        "applicationData": application_data
    }
    
    print(f"🚀 Sending job application request to Stagehand service...")
    print(f"📋 Job: {job_data['title']} at {job_data['company']}")
    print(f"🔗 URL: {job_data['url']}")
    
    try:
        async with httpx.AsyncClient(timeout=600.0) as client:  # 10 minute timeout
            response = await client.post(stagehand_url, json=payload)
            
            if response.status_code == 200:
                result = response.json()
                return result
            else:
                return {
                    "success": False,
                    "message": f"HTTP {response.status_code}: {response.text}",
                    "completed": False
                }
                
    except Exception as e:
        return {
            "success": False,
            "message": f"Request failed: {str(e)}",
            "completed": False,
            "error": str(e)
        }

async def run_stagehand_job_flow():
    """
    Complete job application flow using Stagehand autonomous agents
    """
    
    print("🚀 STAGEHAND AUTONOMOUS FLOW - REAL JOB APPLICATION")
    print("=" * 65)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # Step 1: Check Stagehand service health
        print("⚡ Step 1: Checking Stagehand service...")
        print("-" * 50)
        
        try:
            async with httpx.AsyncClient() as client:
                health_response = await client.get("http://localhost:3001/health")
                if health_response.status_code == 200:
                    health_data = health_response.json()
                    print(f"✅ Stagehand Service: {health_data['status']}")
                    print(f"🕐 Service Time: {health_data['timestamp']}")
                else:
                    print(f"❌ Stagehand service not healthy: {health_response.status_code}")
                    return
        except Exception as e:
            print(f"❌ Cannot connect to Stagehand service: {str(e)}")
            print("Make sure the service is running on port 3001")
            return
        
        print()
        
        # Step 2: Prepare job application data
        print("📋 Step 2: Preparing application data...")
        print("-" * 50)
        
        # Sakshi's profile data
        application_data = {
            "jobId": "demo_job_001",
            "name": "Sakshi Shah",
            "email": "<EMAIL>",
            "phone": "(*************",
            "location": "Delhi, India",
            "background": "Legal professional with LL.M. in Criminal Law, extensive experience at Delhi High Court and Supreme Court Bar Association"
        }
        
        # Target job (using a real job posting for demo)
        job_data = {
            "title": "Legal Consultant",
            "company": "Remote Legal Services",
            "url": "https://www.indeed.com/viewjob?jk=legal-consultant-remote",  # Example URL
            "description": "Legal consultant position for remote work"
        }
        
        print(f"👤 Applicant: {application_data['name']}")
        print(f"📧 Email: {application_data['email']}")
        print(f"📍 Location: {application_data['location']}")
        print(f"🎯 Target Job: {job_data['title']} at {job_data['company']}")
        
        print()
        
        # Step 3: Execute autonomous job application
        print("🤖 Step 3: Executing autonomous job application...")
        print("-" * 50)
        
        print("🔄 Stagehand agent is now working autonomously...")
        print("   • Navigating to job posting")
        print("   • Creating account with email")
        print("   • Filling application form")
        print("   • Handling captchas automatically")
        print("   • Submitting application")
        print()
        
        # This will take several minutes as the agent works autonomously
        result = await apply_to_job_with_stagehand(job_data, application_data)
        
        print("✅ Autonomous application completed!")
        print(f"📊 Success: {result['success']}")
        print(f"📝 Message: {result['message']}")
        print(f"✔️ Completed: {result['completed']}")
        
        if result.get('screenshots'):
            print(f"📸 Screenshots captured: {len(result['screenshots'])}")
            for screenshot in result['screenshots']:
                print(f"   📷 {screenshot}")
        
        if result.get('sessionInfo'):
            session_info = result['sessionInfo']
            print(f"🔗 Browserbase Session: {session_info.get('sessionId', 'N/A')}")
            print(f"🐛 Debug URL: {session_info.get('debugUrl', 'N/A')}")
        
        print()
        
        # Step 4: Summary
        print("📊 Step 4: Application Summary...")
        print("-" * 50)
        
        if result['success']:
            print("🎉 APPLICATION SUCCESSFUL!")
            print("✅ Autonomous agent completed the job application")
            print("✅ All forms were filled automatically")
            print("✅ Application was submitted successfully")
            
            if result.get('agentResult'):
                agent_result = result['agentResult']
                if agent_result.get('actions'):
                    print(f"🔄 Actions performed: {len(agent_result['actions'])}")
                    for i, action in enumerate(agent_result['actions'][:5], 1):  # Show first 5 actions
                        print(f"   {i}. {action}")
        else:
            print("❌ APPLICATION FAILED")
            print(f"Error: {result.get('error', 'Unknown error')}")
            print("Check the debug URL for more details")
        
        print()
        print("🎯 Stagehand Capabilities Demonstrated:")
        print("   ✓ Autonomous navigation and decision making")
        print("   ✓ Intelligent form filling with context awareness")
        print("   ✓ Automatic captcha solving via Browserbase")
        print("   ✓ Email-based account creation (no social login)")
        print("   ✓ Error handling and recovery mechanisms")
        print("   ✓ Session recording for debugging")
        print("   ✓ Screenshot capture at key points")
        print()
        
        print("🎉 STAGEHAND AUTONOMOUS FLOW COMPLETED!")
        print("=" * 65)
        print("🚀 Your AI agent successfully applied to a real job!")
        
        return result
        
    except Exception as e:
        print(f"❌ Error in Stagehand flow: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}

async def demo_multiple_applications():
    """
    Demonstrate applying to multiple jobs
    """
    print("\n🌐 BONUS DEMO: Multiple Job Applications")
    print("-" * 50)
    
    # Sample jobs for demo
    jobs = [
        {
            "title": "Legal Advisor",
            "company": "Tech Startup",
            "url": "https://www.indeed.com/viewjob?jk=legal-advisor-remote",
            "description": "Legal advisor for technology company"
        },
        {
            "title": "Contract Specialist",
            "company": "Consulting Firm", 
            "url": "https://www.indeed.com/viewjob?jk=contract-specialist",
            "description": "Contract review and negotiation specialist"
        }
    ]
    
    application_data = {
        "name": "Sakshi Shah",
        "email": "<EMAIL>",
        "phone": "(*************",
        "location": "Delhi, India",
        "background": "Legal professional with LL.M. in Criminal Law"
    }
    
    results = []
    
    for i, job in enumerate(jobs, 1):
        print(f"\n📋 Application {i}: {job['title']} at {job['company']}")
        
        # Add unique job ID
        app_data = application_data.copy()
        app_data["jobId"] = f"demo_job_{i:03d}"
        
        # Apply to job
        result = await apply_to_job_with_stagehand(job, app_data)
        results.append(result)
        
        print(f"✅ Result: {result['success']}")
        
        # Wait between applications to be respectful
        if i < len(jobs):
            print("⏳ Waiting before next application...")
            await asyncio.sleep(30)  # 30 second delay
    
    # Summary
    successful = sum(1 for r in results if r['success'])
    print(f"\n📊 Batch Application Summary:")
    print(f"   Total Applications: {len(jobs)}")
    print(f"   Successful: {successful}")
    print(f"   Success Rate: {successful/len(jobs)*100:.1f}%")
    
    return results

if __name__ == "__main__":
    print("🎬 Starting Stagehand Autonomous Job Application Demo")
    print("This showcases real AI-powered job applications using Stagehand agents")
    print()
    
    # Run the main flow
    result = asyncio.run(run_stagehand_job_flow())
    
    # Optional: Run multiple applications demo (commented out for safety)
    # if result.get('success'):
    #     print("\n" + "="*50)
    #     batch_results = asyncio.run(demo_multiple_applications())
    
    print(f"\n🏁 Demo completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
