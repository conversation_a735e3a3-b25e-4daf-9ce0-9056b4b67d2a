{"version": 3, "sources": ["../src/index.ts", "../src/ollama-provider.ts", "../src/ollama-chat-language-model.ts", "../src/convert-to-ollama-chat-messages.ts", "../src/generate-tool/infer-tool-calls-from-stream.ts", "../src/map-ollama-finish-reason.ts", "../src/ollama-error.ts", "../src/prepare-tools.ts", "../src/utils/remove-undefined.ts", "../src/utils/response-handler.ts", "../src/utils/text-line-stream.ts", "../src/ollama-embedding-model.ts"], "sourcesContent": ["export type { OllamaProvider, OllamaProviderSettings } from './ollama-provider'\nexport { createOllama, ollama } from './ollama-provider'\n", "import { EmbeddingModelV1, LanguageModelV1, ProviderV1 } from '@ai-sdk/provider'\nimport { withoutTrailingSlash } from '@ai-sdk/provider-utils'\n\nimport { OllamaChatLanguageModel } from '@/ollama-chat-language-model'\nimport { OllamaChatModelId, OllamaChatSettings } from '@/ollama-chat-settings'\nimport { OllamaEmbeddingModel } from '@/ollama-embedding-model'\nimport {\n  OllamaEmbeddingModelId,\n  OllamaEmbeddingSettings,\n} from '@/ollama-embedding-settings'\n\nexport interface OllamaProvider extends ProviderV1 {\n  (modelId: OllamaChatModelId, settings?: OllamaChatSettings): LanguageModelV1\n\n  chat(\n    modelId: OllamaChatModelId,\n    settings?: OllamaChatSettings,\n  ): LanguageModelV1\n\n  embedding(\n    modelId: OllamaEmbeddingModelId,\n    settings?: OllamaEmbeddingSettings,\n  ): EmbeddingModelV1<string>\n\n  languageModel(\n    modelId: OllamaChatModelId,\n    settings?: OllamaChatSettings,\n  ): LanguageModelV1\n\n  textEmbeddingModel(\n    modelId: OllamaEmbeddingModelId,\n    settings?: OllamaEmbeddingSettings,\n  ): EmbeddingModelV1<string>\n}\n\nexport interface OllamaProviderSettings {\n  /**\n   * Base URL for Ollama API calls.\n   */\n  baseURL?: string\n  /**\n   * Custom fetch implementation. You can use it as a middleware to intercept\n   * requests or to provide a custom fetch implementation for e.g. testing\n   */\n  fetch?: typeof fetch\n  /**\n   * @internal\n   */\n  generateId?: () => string\n  /**\n   * Custom headers to include in the requests.\n   */\n  headers?: Record<string, string>\n}\n\nexport function createOllama(\n  options: OllamaProviderSettings = {},\n): OllamaProvider {\n  const baseURL =\n    withoutTrailingSlash(options.baseURL) ?? 'http://127.0.0.1:11434/api'\n\n  const getHeaders = () => ({\n    ...options.headers,\n  })\n\n  const createChatModel = (\n    modelId: OllamaChatModelId,\n    settings: OllamaChatSettings = {},\n  ) =>\n    new OllamaChatLanguageModel(modelId, settings, {\n      baseURL,\n      fetch: options.fetch,\n      headers: getHeaders,\n      provider: 'ollama.chat',\n    })\n\n  const createEmbeddingModel = (\n    modelId: OllamaEmbeddingModelId,\n    settings: OllamaEmbeddingSettings = {},\n  ) =>\n    new OllamaEmbeddingModel(modelId, settings, {\n      baseURL,\n      fetch: options.fetch,\n      headers: getHeaders,\n      provider: 'ollama.embedding',\n    })\n\n  const provider = function (\n    modelId: OllamaChatModelId,\n    settings?: OllamaChatSettings,\n  ) {\n    if (new.target) {\n      throw new Error(\n        'The Ollama model function cannot be called with the new keyword.',\n      )\n    }\n\n    return createChatModel(modelId, settings)\n  }\n\n  provider.chat = createChatModel\n  provider.embedding = createEmbeddingModel\n  provider.languageModel = createChatModel\n  provider.textEmbedding = createEmbeddingModel\n  provider.textEmbeddingModel = createEmbeddingModel\n\n  return provider as OllamaProvider\n}\n\nexport const ollama = createOllama()\n", "/* eslint-disable camelcase */\nimport {\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  LanguageModelV1FinishReason,\n  LanguageModelV1FunctionToolCall,\n  LanguageModelV1StreamPart,\n} from '@ai-sdk/provider'\nimport {\n  combineHeaders,\n  createJsonResponseHand<PERSON>,\n  generateId,\n  ParseR<PERSON>ult,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils'\nimport { z } from 'zod'\n\nimport { convertToOllamaChatMessages } from '@/convert-to-ollama-chat-messages'\nimport { InferToolCallsFromStream } from '@/generate-tool/infer-tool-calls-from-stream'\nimport { mapOllamaFinishReason } from '@/map-ollama-finish-reason'\nimport { OllamaChatModelId, OllamaChatSettings } from '@/ollama-chat-settings'\nimport { ollamaFailedResponseHandler } from '@/ollama-error'\nimport { prepareTools } from '@/prepare-tools'\nimport { createJsonStreamResponseHandler, removeUndefined } from '@/utils'\n\ninterface OllamaChatConfig {\n  baseURL: string\n  fetch?: typeof fetch\n  headers: () => Record<string, string | undefined>\n  provider: string\n}\n\nexport class OllamaChatLanguageModel implements LanguageModelV1 {\n  readonly specificationVersion = 'v1'\n  readonly defaultObjectGenerationMode = 'json'\n  readonly supportsImageUrls = false\n\n  constructor(\n    public readonly modelId: OllamaChatModelId,\n    public readonly settings: OllamaChatSettings,\n    public readonly config: OllamaChatConfig,\n  ) {}\n\n  get supportsStructuredOutputs(): boolean {\n    return this.settings.structuredOutputs ?? false\n  }\n\n  get provider(): string {\n    return this.config.provider\n  }\n\n  private getArguments({\n    frequencyPenalty,\n    maxTokens,\n    mode,\n    presencePenalty,\n    prompt,\n    responseFormat,\n    seed,\n    stopSequences,\n    temperature,\n    topK,\n    topP,\n  }: Parameters<LanguageModelV1['doGenerate']>[0]) {\n    const type = mode.type\n\n    const warnings: LanguageModelV1CallWarning[] = []\n\n    if (\n      responseFormat !== undefined &&\n      responseFormat.type === 'json' &&\n      responseFormat.schema !== undefined &&\n      !this.supportsStructuredOutputs\n    ) {\n      warnings.push({\n        details:\n          'JSON response format schema is only supported with structuredOutputs',\n        setting: 'responseFormat',\n        type: 'unsupported-setting',\n      })\n    }\n\n    const baseArguments = {\n      format: responseFormat?.type,\n      model: this.modelId,\n      options: removeUndefined({\n        f16_kv: this.settings.f16Kv,\n        frequency_penalty: frequencyPenalty,\n        low_vram: this.settings.lowVram,\n        main_gpu: this.settings.mainGpu,\n        min_p: this.settings.minP,\n        mirostat: this.settings.mirostat,\n        mirostat_eta: this.settings.mirostatEta,\n        mirostat_tau: this.settings.mirostatTau,\n        num_batch: this.settings.numBatch,\n        num_ctx: this.settings.numCtx,\n        num_gpu: this.settings.numGpu,\n        num_keep: this.settings.numKeep,\n        num_predict: maxTokens,\n        num_thread: this.settings.numThread,\n        numa: this.settings.numa,\n        penalize_newline: this.settings.penalizeNewline,\n        presence_penalty: presencePenalty,\n        repeat_last_n: this.settings.repeatLastN,\n        repeat_penalty: this.settings.repeatPenalty,\n        seed,\n        stop: stopSequences,\n        temperature,\n        tfs_z: this.settings.tfsZ,\n        top_k: topK,\n        top_p: topP,\n        typical_p: this.settings.typicalP,\n        use_mlock: this.settings.useMlock,\n        use_mmap: this.settings.useMmap,\n        vocab_only: this.settings.vocabOnly,\n      }),\n    }\n\n    switch (type) {\n      case 'regular': {\n        const { tools, toolWarnings } = prepareTools({\n          mode,\n        })\n\n        return {\n          args: {\n            ...baseArguments,\n            messages: convertToOllamaChatMessages(prompt),\n            tools,\n          },\n          type,\n          warnings: [...warnings, ...toolWarnings],\n        }\n      }\n\n      case 'object-json': {\n        return {\n          args: {\n            ...baseArguments,\n            format:\n              this.supportsStructuredOutputs && mode.schema !== undefined\n                ? mode.schema\n                : 'json',\n            messages: convertToOllamaChatMessages(prompt),\n          },\n          type,\n          warnings,\n        }\n      }\n\n      case 'object-tool': {\n        return {\n          args: {\n            ...baseArguments,\n            messages: convertToOllamaChatMessages(prompt),\n            tool_choice: {\n              function: { name: mode.tool.name },\n              type: 'function',\n            },\n            tools: [\n              {\n                function: {\n                  description: mode.tool.description,\n                  name: mode.tool.name,\n                  parameters: mode.tool.parameters,\n                },\n                type: 'function',\n              },\n            ],\n          },\n          type,\n          warnings,\n        }\n      }\n\n      default: {\n        const _exhaustiveCheck: string = type\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`)\n      }\n    }\n  }\n\n  async doGenerate(\n    options: Parameters<LanguageModelV1['doGenerate']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doGenerate']>>> {\n    const { args, warnings } = this.getArguments(options)\n    const body = {\n      ...args,\n      stream: false,\n    }\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      abortSignal: options.abortSignal,\n      body,\n      failedResponseHandler: ollamaFailedResponseHandler,\n      fetch: this.config.fetch,\n      headers: combineHeaders(this.config.headers(), options.headers),\n      successfulResponseHandler: createJsonResponseHandler(\n        ollamaChatResponseSchema,\n      ),\n      url: `${this.config.baseURL}/chat`,\n    })\n\n    const { messages: rawPrompt, ...rawSettings } = body\n\n    const toolCalls: LanguageModelV1FunctionToolCall[] | undefined =\n      response.message.tool_calls?.map((toolCall) => ({\n        args: JSON.stringify(toolCall.function.arguments),\n        toolCallId: toolCall.id ?? generateId(),\n        toolCallType: 'function',\n        toolName: toolCall.function.name,\n      }))\n\n    return {\n      finishReason: mapOllamaFinishReason({\n        finishReason: response.done_reason,\n        hasToolCalls: toolCalls !== undefined && toolCalls.length > 0,\n      }),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      request: { body: JSON.stringify(body) },\n      text: response.message.content ?? undefined,\n      toolCalls,\n      usage: {\n        completionTokens: response.eval_count || 0,\n        promptTokens: response.prompt_eval_count || 0,\n      },\n      warnings,\n    }\n  }\n\n  async doStream(\n    options: Parameters<LanguageModelV1['doStream']>[0],\n  ): Promise<Awaited<ReturnType<LanguageModelV1['doStream']>>> {\n    if (this.settings.simulateStreaming) {\n      const result = await this.doGenerate(options)\n\n      const simulatedStream = new ReadableStream<LanguageModelV1StreamPart>({\n        start(controller) {\n          controller.enqueue({ type: 'response-metadata', ...result.response })\n          if (result.text) {\n            controller.enqueue({\n              textDelta: result.text,\n              type: 'text-delta',\n            })\n          }\n          if (result.toolCalls) {\n            for (const toolCall of result.toolCalls) {\n              controller.enqueue({\n                argsTextDelta: toolCall.args,\n                toolCallId: toolCall.toolCallId,\n                toolCallType: 'function',\n                toolName: toolCall.toolName,\n                type: 'tool-call-delta',\n              })\n\n              controller.enqueue({\n                type: 'tool-call',\n                ...toolCall,\n              })\n            }\n          }\n          controller.enqueue({\n            finishReason: result.finishReason,\n            logprobs: result.logprobs,\n            providerMetadata: result.providerMetadata,\n            type: 'finish',\n            usage: result.usage,\n          })\n          controller.close()\n        },\n      })\n      return {\n        rawCall: result.rawCall,\n        rawResponse: result.rawResponse,\n        stream: simulatedStream,\n        warnings: result.warnings,\n      }\n    }\n\n    const { args: body, type, warnings } = this.getArguments(options)\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      abortSignal: options.abortSignal,\n      body,\n      failedResponseHandler: ollamaFailedResponseHandler,\n      fetch: this.config.fetch,\n      headers: combineHeaders(this.config.headers(), options.headers),\n      successfulResponseHandler: createJsonStreamResponseHandler(\n        ollamaChatStreamChunkSchema,\n      ),\n      url: `${this.config.baseURL}/chat`,\n    })\n\n    const { messages: rawPrompt, ...rawSettings } = body\n\n    const tools =\n      options.mode.type === 'regular'\n        ? options.mode.tools\n        : options.mode.type === 'object-tool'\n          ? [options.mode.tool]\n          : undefined\n\n    const inferToolCallsFromStream = new InferToolCallsFromStream({\n      tools,\n      type,\n    })\n\n    let finishReason: LanguageModelV1FinishReason = 'other'\n    let usage: { completionTokens: number; promptTokens: number } = {\n      completionTokens: Number.NaN,\n      promptTokens: Number.NaN,\n    }\n\n    const { experimentalStreamTools = true } = this.settings\n\n    return {\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      request: { body: JSON.stringify(body) },\n      stream: response.pipeThrough(\n        new TransformStream<\n          ParseResult<z.infer<typeof ollamaChatStreamChunkSchema>>,\n          LanguageModelV1StreamPart\n        >({\n          async flush(controller) {\n            controller.enqueue({\n              finishReason,\n              type: 'finish',\n              usage,\n            })\n          },\n          async transform(chunk, controller) {\n            if (!chunk.success) {\n              controller.enqueue({ error: chunk.error, type: 'error' })\n              return\n            }\n\n            const value = chunk.value\n\n            if (value.done) {\n              finishReason = inferToolCallsFromStream.finish({ controller })\n              usage = {\n                completionTokens: value.eval_count,\n                promptTokens: value.prompt_eval_count || 0,\n              }\n\n              return\n            }\n\n            if (experimentalStreamTools) {\n              const isToolCallStream = inferToolCallsFromStream.parse({\n                controller,\n                delta: value.message.content,\n              })\n\n              if (isToolCallStream) {\n                return\n              }\n            }\n\n            if (value.message.content !== null) {\n              controller.enqueue({\n                textDelta: value.message.content,\n                type: 'text-delta',\n              })\n            }\n          },\n        }),\n      ),\n      warnings,\n    }\n  }\n}\n\nconst ollamaChatResponseSchema = z.object({\n  created_at: z.string(),\n  done: z.literal(true),\n  done_reason: z.string().optional().nullable(),\n  eval_count: z.number(),\n  eval_duration: z.number(),\n  load_duration: z.number().optional(),\n  message: z.object({\n    content: z.string(),\n    role: z.string(),\n    tool_calls: z\n      .array(\n        z.object({\n          function: z.object({\n            arguments: z.record(z.any()),\n            name: z.string(),\n          }),\n          id: z.string().optional(),\n        }),\n      )\n      .optional()\n      .nullable(),\n  }),\n  model: z.string(),\n  prompt_eval_count: z.number().optional(),\n  prompt_eval_duration: z.number().optional(),\n  total_duration: z.number(),\n})\n\nexport type OllamaChatResponseSchema = z.infer<typeof ollamaChatResponseSchema>\n\nconst ollamaChatStreamChunkSchema = z.discriminatedUnion('done', [\n  z.object({\n    created_at: z.string(),\n    done: z.literal(false),\n    message: z.object({\n      content: z.string(),\n      role: z.string(),\n    }),\n    model: z.string(),\n  }),\n  z.object({\n    created_at: z.string(),\n    done: z.literal(true),\n    eval_count: z.number(),\n    eval_duration: z.number(),\n    load_duration: z.number().optional(),\n    model: z.string(),\n    prompt_eval_count: z.number().optional(),\n    prompt_eval_duration: z.number().optional(),\n    total_duration: z.number(),\n  }),\n])\n", "import {\n  LanguageModelV1Prompt,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider'\nimport { convertUint8ArrayToBase64 } from '@ai-sdk/provider-utils'\n\nimport { OllamaChatPrompt } from '@/ollama-chat-prompt'\n\nexport function convertToOllamaChatMessages(\n  prompt: LanguageModelV1Prompt,\n): OllamaChatPrompt {\n  const messages: OllamaChatPrompt = []\n\n  for (const { content, role } of prompt) {\n    switch (role) {\n      case 'system': {\n        messages.push({ content, role: 'system' })\n        break\n      }\n\n      case 'user': {\n        messages.push({\n          ...content.reduce<{ content: string; images?: string[] }>(\n            (previous, current) => {\n              if (current.type === 'text') {\n                previous.content += current.text\n              } else if (\n                current.type === 'image' &&\n                current.image instanceof URL\n              ) {\n                throw new UnsupportedFunctionalityError({\n                  functionality: 'Image URLs in user messages',\n                })\n              } else if (\n                current.type === 'image' &&\n                current.image instanceof Uint8Array\n              ) {\n                previous.images = previous.images || []\n                previous.images.push(convertUint8ArrayToBase64(current.image))\n              }\n\n              return previous\n            },\n            { content: '' },\n          ),\n          role: 'user',\n        })\n        break\n      }\n\n      case 'assistant': {\n        const text: Array<string> = []\n        const toolCalls: Array<{\n          function: { arguments: object; name: string }\n          id: string\n          type: 'function'\n        }> = []\n\n        for (const part of content) {\n          switch (part.type) {\n            case 'text': {\n              text.push(part.text)\n              break\n            }\n            case 'tool-call': {\n              toolCalls.push({\n                function: {\n                  arguments: part.args as object,\n                  name: part.toolName,\n                },\n                id: part.toolCallId,\n                type: 'function',\n              })\n              break\n            }\n            default: {\n              const _exhaustiveCheck: never = part\n              throw new Error(`Unsupported part: ${_exhaustiveCheck}`)\n            }\n          }\n        }\n\n        messages.push({\n          content: text.join(','),\n          role: 'assistant',\n          tool_calls: toolCalls.length > 0 ? toolCalls : undefined,\n        })\n\n        break\n      }\n\n      case 'tool': {\n        messages.push(\n          ...content.map((part) => ({\n            // Non serialized contents are not accepted by ollama, triggering the following error:\n            // \"json: cannot unmarshal array into Go struct field ChatRequest.messages of type string\"\n            content:\n              typeof part.result === 'object'\n                ? JSON.stringify(part.result)\n                : `${part.result}`,\n            role: 'tool' as const,\n            tool_call_id: part.toolCallId,\n          })),\n        )\n        break\n      }\n\n      default: {\n        const _exhaustiveCheck: string = role\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`)\n      }\n    }\n  }\n\n  return messages\n}\n", "import type {\n  LanguageModelV1CallOptions,\n  LanguageModelV1FinishReason,\n  LanguageModelV1FunctionTool,\n  LanguageModelV1ProviderDefinedTool,\n  LanguageModelV1StreamPart,\n} from '@ai-sdk/provider'\nimport { generateId } from '@ai-sdk/provider-utils'\nimport { parse } from 'partial-json'\n\ntype ToolCall = {\n  function: {\n    arguments: string\n    name: string\n  }\n  id: string\n  type: 'function'\n}\n\nexport type CallModeType = LanguageModelV1CallOptions['mode']['type']\n\nexport class InferToolCallsFromStream {\n  private _firstMessage: boolean\n  private readonly _toolCalls: ToolCall[]\n  private readonly _tools?: (\n    | LanguageModelV1FunctionTool\n    | LanguageModelV1ProviderDefinedTool\n  )[]\n  private _toolPartial: string\n  private readonly _type: CallModeType\n  private _detectedToolCall: boolean\n\n  constructor({\n    tools,\n    type,\n  }: {\n    tools:\n      | (LanguageModelV1FunctionTool | LanguageModelV1ProviderDefinedTool)[]\n      | undefined\n    type: CallModeType\n  }) {\n    this._firstMessage = true\n    this._tools = tools\n    this._toolPartial = ''\n    this._toolCalls = []\n    this._type = type\n    this._detectedToolCall = false\n  }\n\n  get toolCalls(): ToolCall[] {\n    return this._toolCalls\n  }\n\n  get detectedToolCall(): boolean {\n    return this._detectedToolCall\n  }\n\n  parse({\n    controller,\n    delta,\n  }: {\n    controller: TransformStreamDefaultController<LanguageModelV1StreamPart>\n    delta: string\n  }): boolean {\n    this.detectToolCall(delta)\n\n    if (!this._detectedToolCall) {\n      return false\n    }\n\n    this._toolPartial += delta\n\n    let parsedFunctions = parse(this._toolPartial)\n    if (!Array.isArray(parsedFunctions)) {\n      parsedFunctions = [parsedFunctions]\n    }\n\n    for (const [index, parsedFunction] of parsedFunctions.entries()) {\n      const parsedArguments = JSON.stringify(parsedFunction?.parameters) ?? ''\n\n      if (parsedArguments === '') {\n        continue\n      }\n\n      if (!this._toolCalls[index]) {\n        this._toolCalls[index] = {\n          function: {\n            arguments: '',\n            name: parsedFunction.name,\n          },\n          id: generateId(),\n          type: 'function',\n        }\n      }\n\n      const toolCall = this._toolCalls[index]\n      toolCall.function.arguments = parsedArguments\n\n      controller.enqueue({\n        argsTextDelta: delta,\n        toolCallId: toolCall.id,\n        toolCallType: 'function',\n        toolName: toolCall.function.name,\n        type: 'tool-call-delta',\n      })\n    }\n\n    return true\n  }\n\n  finish({\n    controller,\n  }: {\n    controller: TransformStreamDefaultController<LanguageModelV1StreamPart>\n  }): LanguageModelV1FinishReason {\n    for (const toolCall of this.toolCalls) {\n      controller.enqueue({\n        args: toolCall.function.arguments,\n        toolCallId: toolCall.id,\n        toolCallType: 'function',\n        toolName: toolCall.function.name,\n        type: 'tool-call',\n      })\n    }\n\n    return this.finishReason()\n  }\n\n  private detectToolCall(delta: string) {\n    if (!this._tools || this._tools.length === 0) {\n      return\n    }\n\n    if (this._firstMessage) {\n      if (this._type === 'object-tool') {\n        this._detectedToolCall = true\n      } else if (\n        this._type === 'regular' &&\n        (delta.trim().startsWith('{') || delta.trim().startsWith('['))\n      ) {\n        this._detectedToolCall = true\n      }\n\n      this._firstMessage = false\n    }\n  }\n\n  private finishReason(): LanguageModelV1FinishReason {\n    if (!this.detectedToolCall) {\n      return 'stop'\n    }\n\n    return this._type === 'object-tool' ? 'stop' : 'tool-calls'\n  }\n}\n", "import { LanguageModelV1FinishReason } from '@ai-sdk/provider'\n\nexport function mapOllamaFinishReason({\n  finishReason,\n  hasToolCalls,\n}: {\n  finishReason: string | null | undefined\n  hasToolCalls: boolean\n}): LanguageModelV1FinishReason {\n  switch (finishReason) {\n    case 'stop': {\n      return hasToolCalls ? 'tool-calls' : 'stop'\n    }\n    default: {\n      return 'other'\n    }\n  }\n}\n", "import { createJsonErrorResponseHandler } from '@ai-sdk/provider-utils'\nimport { z } from 'zod'\n\nconst ollamaErrorDataSchema = z.object({\n  error: z.object({\n    code: z.string().nullable(),\n    message: z.string(),\n    param: z.any().nullable(),\n    type: z.string(),\n  }),\n})\n\nexport type OllamaErrorData = z.infer<typeof ollamaErrorDataSchema>\n\nexport const ollamaFailedResponseHandler = createJsonErrorResponseHandler({\n  errorSchema: ollamaErrorDataSchema,\n  errorToMessage: (data) => data.error.message,\n})\n", "import {\n  LanguageModelV1,\n  LanguageModelV1CallWarning,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider'\n\nexport function prepareTools({\n  mode,\n}: {\n  mode: Parameters<LanguageModelV1['doGenerate']>[0]['mode'] & {\n    type: 'regular'\n  }\n}): {\n  tools:\n    | undefined\n    | {\n        function: {\n          description: string | undefined\n          name: string\n          parameters: unknown\n        }\n        type: 'function'\n      }[]\n  toolWarnings: LanguageModelV1CallWarning[]\n} {\n  const tools = mode.tools?.length ? mode.tools : undefined\n  const toolWarnings: LanguageModelV1CallWarning[] = []\n\n  const toolChoice = mode.toolChoice\n\n  if (tools === undefined) {\n    return {\n      tools: undefined,\n      toolWarnings,\n    }\n  }\n\n  const ollamaTools: {\n    function: {\n      description: string | undefined\n      name: string\n      parameters: unknown\n    }\n    type: 'function'\n  }[] = []\n\n  for (const tool of tools) {\n    if (tool.type === 'provider-defined') {\n      toolWarnings.push({ tool, type: 'unsupported-tool' })\n    } else {\n      ollamaTools.push({\n        function: {\n          description: tool.description,\n          name: tool.name,\n          parameters: tool.parameters,\n        },\n        type: 'function',\n      })\n    }\n  }\n\n  if (toolChoice === undefined) {\n    return {\n      tools: ollamaTools,\n      toolWarnings,\n    }\n  }\n\n  const type = toolChoice.type\n\n  switch (type) {\n    case 'auto': {\n      return {\n        tools: ollamaTools,\n        toolWarnings,\n      }\n    }\n    case 'none': {\n      return {\n        tools: undefined,\n        toolWarnings,\n      }\n    }\n    default: {\n      const _exhaustiveCheck: string = type\n      throw new UnsupportedFunctionalityError({\n        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`,\n      })\n    }\n  }\n}\n", "export function removeUndefined(object: object) {\n  return Object.fromEntries(\n    Object.entries(object).filter(([, v]) => v !== undefined),\n  )\n}\n", "import { EmptyResponseBodyError } from '@ai-sdk/provider'\nimport {\n  extractResponseHeaders,\n  ParseResult,\n  ResponseHandler,\n  safeParseJSON,\n} from '@ai-sdk/provider-utils'\nimport { ZodSchema } from 'zod'\n\nimport { TextLineStream } from '@/utils/text-line-stream'\n\nexport const createJsonStreamResponseHandler =\n  <T>(\n    chunkSchema: ZodSchema<T>,\n  ): ResponseHandler<ReadableStream<ParseResult<T>>> =>\n  async ({ response }: { response: Response }) => {\n    const responseHeaders = extractResponseHeaders(response)\n\n    if (response.body === null) {\n      throw new EmptyResponseBodyError({})\n    }\n\n    return {\n      responseHeaders,\n      value: response.body\n        .pipeThrough(new TextDecoderStream())\n        .pipeThrough(new TextLineStream())\n        .pipeThrough(\n          new TransformStream<string, ParseResult<T>>({\n            transform(chunkText, controller) {\n              controller.enqueue(\n                safeParseJSON({\n                  schema: chunkSchema,\n                  text: chunkText,\n                }),\n              )\n            },\n          }),\n        ),\n    }\n  }\n", "export class TextLineStream extends TransformStream<string, string> {\n  private buffer = ''\n\n  constructor() {\n    super({\n      flush: (controller) => {\n        if (this.buffer.length === 0) return\n\n        controller.enqueue(this.buffer)\n      },\n      transform: (chunkText, controller) => {\n        chunkText = this.buffer + chunkText\n\n        while (true) {\n          const EOL = chunkText.indexOf('\\n')\n\n          if (EOL === -1) break\n\n          controller.enqueue(chunkText.slice(0, EOL))\n          chunkText = chunkText.slice(EOL + 1)\n        }\n\n        this.buffer = chunkText\n      },\n    })\n  }\n}\n", "import {\n  EmbeddingModelV1,\n  TooManyEmbeddingValuesForCallError,\n} from '@ai-sdk/provider'\nimport {\n  createJsonResponseHandler,\n  postJsonToApi,\n} from '@ai-sdk/provider-utils'\nimport { z } from 'zod'\n\nimport {\n  OllamaEmbeddingModelId,\n  OllamaEmbeddingSettings,\n} from '@/ollama-embedding-settings'\nimport { ollamaFailedResponseHandler } from '@/ollama-error'\n\ntype OllamaEmbeddingConfig = {\n  baseURL: string\n  fetch?: typeof fetch\n  headers: () => Record<string, string | undefined>\n  provider: string\n}\nexport class OllamaEmbeddingModel implements EmbeddingModelV1<string> {\n  readonly specificationVersion = 'v1'\n  readonly modelId: OllamaEmbeddingModelId\n\n  private readonly config: OllamaEmbeddingConfig\n  private readonly settings: OllamaEmbeddingSettings\n\n  get provider(): string {\n    return this.config.provider\n  }\n\n  get maxEmbeddingsPerCall(): number {\n    return this.settings.maxEmbeddingsPerCall ?? 2048\n  }\n\n  get supportsParallelCalls(): boolean {\n    return false\n  }\n\n  constructor(\n    modelId: OllamaEmbeddingModelId,\n    settings: OllamaEmbeddingSettings,\n    config: OllamaEmbeddingConfig,\n  ) {\n    this.modelId = modelId\n    this.settings = settings\n    this.config = config\n  }\n\n  async doEmbed({\n    abortSignal,\n    values,\n  }: Parameters<EmbeddingModelV1<string>['doEmbed']>[0]): Promise<\n    Awaited<ReturnType<EmbeddingModelV1<string>['doEmbed']>>\n  > {\n    if (values.length > this.maxEmbeddingsPerCall) {\n      throw new TooManyEmbeddingValuesForCallError({\n        maxEmbeddingsPerCall: this.maxEmbeddingsPerCall,\n        modelId: this.modelId,\n        provider: this.provider,\n        values,\n      })\n    }\n\n    const { responseHeaders, value: response } = await postJsonToApi({\n      abortSignal,\n      body: {\n        input: values,\n        model: this.modelId,\n      },\n      failedResponseHandler: ollamaFailedResponseHandler,\n      fetch: this.config.fetch,\n      headers: this.config.headers(),\n      successfulResponseHandler: createJsonResponseHandler(\n        ollamaTextEmbeddingResponseSchema,\n      ),\n      url: `${this.config.baseURL}/embed`,\n    })\n\n    return {\n      embeddings: response.embeddings,\n      rawResponse: { headers: responseHeaders },\n      usage: response.prompt_eval_count\n        ? { tokens: response.prompt_eval_count }\n        : undefined,\n    }\n  }\n}\n\nconst ollamaTextEmbeddingResponseSchema = z.object({\n  embeddings: z.array(z.array(z.number())),\n  prompt_eval_count: z.number().nullable(),\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACCA,IAAAA,yBAAqC;;;ACOrC,IAAAC,yBAMO;AACP,IAAAC,cAAkB;;;ACflB,sBAGO;AACP,4BAA0C;AAInC,SAAS,4BACd,QACkB;AAClB,QAAM,WAA6B,CAAC;AAEpC,aAAW,EAAE,SAAS,KAAK,KAAK,QAAQ;AACtC,YAAQ,MAAM;AAAA,MACZ,KAAK,UAAU;AACb,iBAAS,KAAK,EAAE,SAAS,MAAM,SAAS,CAAC;AACzC;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AACX,iBAAS,KAAK;AAAA,UACZ,GAAG,QAAQ;AAAA,YACT,CAAC,UAAU,YAAY;AACrB,kBAAI,QAAQ,SAAS,QAAQ;AAC3B,yBAAS,WAAW,QAAQ;AAAA,cAC9B,WACE,QAAQ,SAAS,WACjB,QAAQ,iBAAiB,KACzB;AACA,sBAAM,IAAI,8CAA8B;AAAA,kBACtC,eAAe;AAAA,gBACjB,CAAC;AAAA,cACH,WACE,QAAQ,SAAS,WACjB,QAAQ,iBAAiB,YACzB;AACA,yBAAS,SAAS,SAAS,UAAU,CAAC;AACtC,yBAAS,OAAO,SAAK,iDAA0B,QAAQ,KAAK,CAAC;AAAA,cAC/D;AAEA,qBAAO;AAAA,YACT;AAAA,YACA,EAAE,SAAS,GAAG;AAAA,UAChB;AAAA,UACA,MAAM;AAAA,QACR,CAAC;AACD;AAAA,MACF;AAAA,MAEA,KAAK,aAAa;AAChB,cAAM,OAAsB,CAAC;AAC7B,cAAM,YAID,CAAC;AAEN,mBAAW,QAAQ,SAAS;AAC1B,kBAAQ,KAAK,MAAM;AAAA,YACjB,KAAK,QAAQ;AACX,mBAAK,KAAK,KAAK,IAAI;AACnB;AAAA,YACF;AAAA,YACA,KAAK,aAAa;AAChB,wBAAU,KAAK;AAAA,gBACb,UAAU;AAAA,kBACR,WAAW,KAAK;AAAA,kBAChB,MAAM,KAAK;AAAA,gBACb;AAAA,gBACA,IAAI,KAAK;AAAA,gBACT,MAAM;AAAA,cACR,CAAC;AACD;AAAA,YACF;AAAA,YACA,SAAS;AACP,oBAAM,mBAA0B;AAChC,oBAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,YACzD;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,KAAK;AAAA,UACZ,SAAS,KAAK,KAAK,GAAG;AAAA,UACtB,MAAM;AAAA,UACN,YAAY,UAAU,SAAS,IAAI,YAAY;AAAA,QACjD,CAAC;AAED;AAAA,MACF;AAAA,MAEA,KAAK,QAAQ;AACX,iBAAS;AAAA,UACP,GAAG,QAAQ,IAAI,CAAC,UAAU;AAAA;AAAA;AAAA,YAGxB,SACE,OAAO,KAAK,WAAW,WACnB,KAAK,UAAU,KAAK,MAAM,IAC1B,GAAG,KAAK,MAAM;AAAA,YACpB,MAAM;AAAA,YACN,cAAc,KAAK;AAAA,UACrB,EAAE;AAAA,QACJ;AACA;AAAA,MACF;AAAA,MAEA,SAAS;AACP,cAAM,mBAA2B;AACjC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;AC5GA,IAAAC,yBAA2B;AAC3B,0BAAsB;AAaf,IAAM,2BAAN,MAA+B;AAAA,EAWpC,YAAY;AAAA,IACV;AAAA,IACA;AAAA,EACF,GAKG;AACD,SAAK,gBAAgB;AACrB,SAAK,SAAS;AACd,SAAK,eAAe;AACpB,SAAK,aAAa,CAAC;AACnB,SAAK,QAAQ;AACb,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EAEA,IAAI,YAAwB;AAC1B,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,mBAA4B;AAC9B,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,MAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,GAGY;AA/Dd;AAgEI,SAAK,eAAe,KAAK;AAEzB,QAAI,CAAC,KAAK,mBAAmB;AAC3B,aAAO;AAAA,IACT;AAEA,SAAK,gBAAgB;AAErB,QAAI,sBAAkB,2BAAM,KAAK,YAAY;AAC7C,QAAI,CAAC,MAAM,QAAQ,eAAe,GAAG;AACnC,wBAAkB,CAAC,eAAe;AAAA,IACpC;AAEA,eAAW,CAAC,OAAO,cAAc,KAAK,gBAAgB,QAAQ,GAAG;AAC/D,YAAM,mBAAkB,UAAK,UAAU,iDAAgB,UAAU,MAAzC,YAA8C;AAEtE,UAAI,oBAAoB,IAAI;AAC1B;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,WAAW,KAAK,GAAG;AAC3B,aAAK,WAAW,KAAK,IAAI;AAAA,UACvB,UAAU;AAAA,YACR,WAAW;AAAA,YACX,MAAM,eAAe;AAAA,UACvB;AAAA,UACA,QAAI,mCAAW;AAAA,UACf,MAAM;AAAA,QACR;AAAA,MACF;AAEA,YAAM,WAAW,KAAK,WAAW,KAAK;AACtC,eAAS,SAAS,YAAY;AAE9B,iBAAW,QAAQ;AAAA,QACjB,eAAe;AAAA,QACf,YAAY,SAAS;AAAA,QACrB,cAAc;AAAA,QACd,UAAU,SAAS,SAAS;AAAA,QAC5B,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO;AAAA,IACL;AAAA,EACF,GAEgC;AAC9B,eAAW,YAAY,KAAK,WAAW;AACrC,iBAAW,QAAQ;AAAA,QACjB,MAAM,SAAS,SAAS;AAAA,QACxB,YAAY,SAAS;AAAA,QACrB,cAAc;AAAA,QACd,UAAU,SAAS,SAAS;AAAA,QAC5B,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAEA,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA,EAEQ,eAAe,OAAe;AACpC,QAAI,CAAC,KAAK,UAAU,KAAK,OAAO,WAAW,GAAG;AAC5C;AAAA,IACF;AAEA,QAAI,KAAK,eAAe;AACtB,UAAI,KAAK,UAAU,eAAe;AAChC,aAAK,oBAAoB;AAAA,MAC3B,WACE,KAAK,UAAU,cACd,MAAM,KAAK,EAAE,WAAW,GAAG,KAAK,MAAM,KAAK,EAAE,WAAW,GAAG,IAC5D;AACA,aAAK,oBAAoB;AAAA,MAC3B;AAEA,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EAEQ,eAA4C;AAClD,QAAI,CAAC,KAAK,kBAAkB;AAC1B,aAAO;AAAA,IACT;AAEA,WAAO,KAAK,UAAU,gBAAgB,SAAS;AAAA,EACjD;AACF;;;ACxJO,SAAS,sBAAsB;AAAA,EACpC;AAAA,EACA;AACF,GAGgC;AAC9B,UAAQ,cAAc;AAAA,IACpB,KAAK,QAAQ;AACX,aAAO,eAAe,eAAe;AAAA,IACvC;AAAA,IACA,SAAS;AACP,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;ACjBA,IAAAC,yBAA+C;AAC/C,iBAAkB;AAElB,IAAM,wBAAwB,aAAE,OAAO;AAAA,EACrC,OAAO,aAAE,OAAO;AAAA,IACd,MAAM,aAAE,OAAO,EAAE,SAAS;AAAA,IAC1B,SAAS,aAAE,OAAO;AAAA,IAClB,OAAO,aAAE,IAAI,EAAE,SAAS;AAAA,IACxB,MAAM,aAAE,OAAO;AAAA,EACjB,CAAC;AACH,CAAC;AAIM,IAAM,kCAA8B,uDAA+B;AAAA,EACxE,aAAa;AAAA,EACb,gBAAgB,CAAC,SAAS,KAAK,MAAM;AACvC,CAAC;;;ACjBD,IAAAC,mBAIO;AAEA,SAAS,aAAa;AAAA,EAC3B;AACF,GAgBE;AAxBF;AAyBE,QAAM,UAAQ,UAAK,UAAL,mBAAY,UAAS,KAAK,QAAQ;AAChD,QAAM,eAA6C,CAAC;AAEpD,QAAM,aAAa,KAAK;AAExB,MAAI,UAAU,QAAW;AACvB,WAAO;AAAA,MACL,OAAO;AAAA,MACP;AAAA,IACF;AAAA,EACF;AAEA,QAAM,cAOA,CAAC;AAEP,aAAW,QAAQ,OAAO;AACxB,QAAI,KAAK,SAAS,oBAAoB;AACpC,mBAAa,KAAK,EAAE,MAAM,MAAM,mBAAmB,CAAC;AAAA,IACtD,OAAO;AACL,kBAAY,KAAK;AAAA,QACf,UAAU;AAAA,UACR,aAAa,KAAK;AAAA,UAClB,MAAM,KAAK;AAAA,UACX,YAAY,KAAK;AAAA,QACnB;AAAA,QACA,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,eAAe,QAAW;AAC5B,WAAO;AAAA,MACL,OAAO;AAAA,MACP;AAAA,IACF;AAAA,EACF;AAEA,QAAM,OAAO,WAAW;AAExB,UAAQ,MAAM;AAAA,IACZ,KAAK,QAAQ;AACX,aAAO;AAAA,QACL,OAAO;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,IACA,KAAK,QAAQ;AACX,aAAO;AAAA,QACL,OAAO;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA,IACA,SAAS;AACP,YAAM,mBAA2B;AACjC,YAAM,IAAI,+CAA8B;AAAA,QACtC,eAAe,iCAAiC,gBAAgB;AAAA,MAClE,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;AC1FO,SAAS,gBAAgB,QAAgB;AAC9C,SAAO,OAAO;AAAA,IACZ,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,MAAM,MAAS;AAAA,EAC1D;AACF;;;ACJA,IAAAC,mBAAuC;AACvC,IAAAC,yBAKO;;;ACNA,IAAM,iBAAN,cAA6B,gBAAgC;AAAA,EAGlE,cAAc;AACZ,UAAM;AAAA,MACJ,OAAO,CAAC,eAAe;AACrB,YAAI,KAAK,OAAO,WAAW,EAAG;AAE9B,mBAAW,QAAQ,KAAK,MAAM;AAAA,MAChC;AAAA,MACA,WAAW,CAAC,WAAW,eAAe;AACpC,oBAAY,KAAK,SAAS;AAE1B,eAAO,MAAM;AACX,gBAAM,MAAM,UAAU,QAAQ,IAAI;AAElC,cAAI,QAAQ,GAAI;AAEhB,qBAAW,QAAQ,UAAU,MAAM,GAAG,GAAG,CAAC;AAC1C,sBAAY,UAAU,MAAM,MAAM,CAAC;AAAA,QACrC;AAEA,aAAK,SAAS;AAAA,MAChB;AAAA,IACF,CAAC;AAvBH,SAAQ,SAAS;AAAA,EAwBjB;AACF;;;ADfO,IAAM,kCACX,CACE,gBAEF,OAAO,EAAE,SAAS,MAA8B;AAC9C,QAAM,sBAAkB,+CAAuB,QAAQ;AAEvD,MAAI,SAAS,SAAS,MAAM;AAC1B,UAAM,IAAI,wCAAuB,CAAC,CAAC;AAAA,EACrC;AAEA,SAAO;AAAA,IACL;AAAA,IACA,OAAO,SAAS,KACb,YAAY,IAAI,kBAAkB,CAAC,EACnC,YAAY,IAAI,eAAe,CAAC,EAChC;AAAA,MACC,IAAI,gBAAwC;AAAA,QAC1C,UAAU,WAAW,YAAY;AAC/B,qBAAW;AAAA,gBACT,sCAAc;AAAA,cACZ,QAAQ;AAAA,cACR,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACJ;AACF;;;APRK,IAAM,0BAAN,MAAyD;AAAA,EAK9D,YACkB,SACA,UACA,QAChB;AAHgB;AACA;AACA;AAPlB,SAAS,uBAAuB;AAChC,SAAS,8BAA8B;AACvC,SAAS,oBAAoB;AAAA,EAM1B;AAAA,EAEH,IAAI,4BAAqC;AA3C3C;AA4CI,YAAO,UAAK,SAAS,sBAAd,YAAmC;AAAA,EAC5C;AAAA,EAEA,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EAEQ,aAAa;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAiD;AAC/C,UAAM,OAAO,KAAK;AAElB,UAAM,WAAyC,CAAC;AAEhD,QACE,mBAAmB,UACnB,eAAe,SAAS,UACxB,eAAe,WAAW,UAC1B,CAAC,KAAK,2BACN;AACA,eAAS,KAAK;AAAA,QACZ,SACE;AAAA,QACF,SAAS;AAAA,QACT,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAEA,UAAM,gBAAgB;AAAA,MACpB,QAAQ,iDAAgB;AAAA,MACxB,OAAO,KAAK;AAAA,MACZ,SAAS,gBAAgB;AAAA,QACvB,QAAQ,KAAK,SAAS;AAAA,QACtB,mBAAmB;AAAA,QACnB,UAAU,KAAK,SAAS;AAAA,QACxB,UAAU,KAAK,SAAS;AAAA,QACxB,OAAO,KAAK,SAAS;AAAA,QACrB,UAAU,KAAK,SAAS;AAAA,QACxB,cAAc,KAAK,SAAS;AAAA,QAC5B,cAAc,KAAK,SAAS;AAAA,QAC5B,WAAW,KAAK,SAAS;AAAA,QACzB,SAAS,KAAK,SAAS;AAAA,QACvB,SAAS,KAAK,SAAS;AAAA,QACvB,UAAU,KAAK,SAAS;AAAA,QACxB,aAAa;AAAA,QACb,YAAY,KAAK,SAAS;AAAA,QAC1B,MAAM,KAAK,SAAS;AAAA,QACpB,kBAAkB,KAAK,SAAS;AAAA,QAChC,kBAAkB;AAAA,QAClB,eAAe,KAAK,SAAS;AAAA,QAC7B,gBAAgB,KAAK,SAAS;AAAA,QAC9B;AAAA,QACA,MAAM;AAAA,QACN;AAAA,QACA,OAAO,KAAK,SAAS;AAAA,QACrB,OAAO;AAAA,QACP,OAAO;AAAA,QACP,WAAW,KAAK,SAAS;AAAA,QACzB,WAAW,KAAK,SAAS;AAAA,QACzB,UAAU,KAAK,SAAS;AAAA,QACxB,YAAY,KAAK,SAAS;AAAA,MAC5B,CAAC;AAAA,IACH;AAEA,YAAQ,MAAM;AAAA,MACZ,KAAK,WAAW;AACd,cAAM,EAAE,OAAO,aAAa,IAAI,aAAa;AAAA,UAC3C;AAAA,QACF,CAAC;AAED,eAAO;AAAA,UACL,MAAM;AAAA,YACJ,GAAG;AAAA,YACH,UAAU,4BAA4B,MAAM;AAAA,YAC5C;AAAA,UACF;AAAA,UACA;AAAA,UACA,UAAU,CAAC,GAAG,UAAU,GAAG,YAAY;AAAA,QACzC;AAAA,MACF;AAAA,MAEA,KAAK,eAAe;AAClB,eAAO;AAAA,UACL,MAAM;AAAA,YACJ,GAAG;AAAA,YACH,QACE,KAAK,6BAA6B,KAAK,WAAW,SAC9C,KAAK,SACL;AAAA,YACN,UAAU,4BAA4B,MAAM;AAAA,UAC9C;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,MAEA,KAAK,eAAe;AAClB,eAAO;AAAA,UACL,MAAM;AAAA,YACJ,GAAG;AAAA,YACH,UAAU,4BAA4B,MAAM;AAAA,YAC5C,aAAa;AAAA,cACX,UAAU,EAAE,MAAM,KAAK,KAAK,KAAK;AAAA,cACjC,MAAM;AAAA,YACR;AAAA,YACA,OAAO;AAAA,cACL;AAAA,gBACE,UAAU;AAAA,kBACR,aAAa,KAAK,KAAK;AAAA,kBACvB,MAAM,KAAK,KAAK;AAAA,kBAChB,YAAY,KAAK,KAAK;AAAA,gBACxB;AAAA,gBACA,MAAM;AAAA,cACR;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,MAEA,SAAS;AACP,cAAM,mBAA2B;AACjC,cAAM,IAAI,MAAM,qBAAqB,gBAAgB,EAAE;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,WACJ,SAC6D;AAxLjE;AAyLI,UAAM,EAAE,MAAM,SAAS,IAAI,KAAK,aAAa,OAAO;AACpD,UAAM,OAAO;AAAA,MACX,GAAG;AAAA,MACH,QAAQ;AAAA,IACV;AAEA,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,UAAM,sCAAc;AAAA,MAC/D,aAAa,QAAQ;AAAA,MACrB;AAAA,MACA,uBAAuB;AAAA,MACvB,OAAO,KAAK,OAAO;AAAA,MACnB,aAAS,uCAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;AAAA,MAC9D,+BAA2B;AAAA,QACzB;AAAA,MACF;AAAA,MACA,KAAK,GAAG,KAAK,OAAO,OAAO;AAAA,IAC7B,CAAC;AAED,UAAM,EAAE,UAAU,WAAW,GAAG,YAAY,IAAI;AAEhD,UAAM,aACJ,cAAS,QAAQ,eAAjB,mBAA6B,IAAI,CAAC,aAAU;AA9MlD,UAAAC;AA8MsD;AAAA,QAC9C,MAAM,KAAK,UAAU,SAAS,SAAS,SAAS;AAAA,QAChD,aAAYA,MAAA,SAAS,OAAT,OAAAA,UAAe,mCAAW;AAAA,QACtC,cAAc;AAAA,QACd,UAAU,SAAS,SAAS;AAAA,MAC9B;AAAA;AAEF,WAAO;AAAA,MACL,cAAc,sBAAsB;AAAA,QAClC,cAAc,SAAS;AAAA,QACvB,cAAc,cAAc,UAAa,UAAU,SAAS;AAAA,MAC9D,CAAC;AAAA,MACD,SAAS,EAAE,WAAW,YAAY;AAAA,MAClC,aAAa,EAAE,SAAS,gBAAgB;AAAA,MACxC,SAAS,EAAE,MAAM,KAAK,UAAU,IAAI,EAAE;AAAA,MACtC,OAAM,cAAS,QAAQ,YAAjB,YAA4B;AAAA,MAClC;AAAA,MACA,OAAO;AAAA,QACL,kBAAkB,SAAS,cAAc;AAAA,QACzC,cAAc,SAAS,qBAAqB;AAAA,MAC9C;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,SACJ,SAC2D;AAC3D,QAAI,KAAK,SAAS,mBAAmB;AACnC,YAAM,SAAS,MAAM,KAAK,WAAW,OAAO;AAE5C,YAAM,kBAAkB,IAAI,eAA0C;AAAA,QACpE,MAAM,YAAY;AAChB,qBAAW,QAAQ,EAAE,MAAM,qBAAqB,GAAG,OAAO,SAAS,CAAC;AACpE,cAAI,OAAO,MAAM;AACf,uBAAW,QAAQ;AAAA,cACjB,WAAW,OAAO;AAAA,cAClB,MAAM;AAAA,YACR,CAAC;AAAA,UACH;AACA,cAAI,OAAO,WAAW;AACpB,uBAAW,YAAY,OAAO,WAAW;AACvC,yBAAW,QAAQ;AAAA,gBACjB,eAAe,SAAS;AAAA,gBACxB,YAAY,SAAS;AAAA,gBACrB,cAAc;AAAA,gBACd,UAAU,SAAS;AAAA,gBACnB,MAAM;AAAA,cACR,CAAC;AAED,yBAAW,QAAQ;AAAA,gBACjB,MAAM;AAAA,gBACN,GAAG;AAAA,cACL,CAAC;AAAA,YACH;AAAA,UACF;AACA,qBAAW,QAAQ;AAAA,YACjB,cAAc,OAAO;AAAA,YACrB,UAAU,OAAO;AAAA,YACjB,kBAAkB,OAAO;AAAA,YACzB,MAAM;AAAA,YACN,OAAO,OAAO;AAAA,UAChB,CAAC;AACD,qBAAW,MAAM;AAAA,QACnB;AAAA,MACF,CAAC;AACD,aAAO;AAAA,QACL,SAAS,OAAO;AAAA,QAChB,aAAa,OAAO;AAAA,QACpB,QAAQ;AAAA,QACR,UAAU,OAAO;AAAA,MACnB;AAAA,IACF;AAEA,UAAM,EAAE,MAAM,MAAM,MAAM,SAAS,IAAI,KAAK,aAAa,OAAO;AAEhE,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,UAAM,sCAAc;AAAA,MAC/D,aAAa,QAAQ;AAAA,MACrB;AAAA,MACA,uBAAuB;AAAA,MACvB,OAAO,KAAK,OAAO;AAAA,MACnB,aAAS,uCAAe,KAAK,OAAO,QAAQ,GAAG,QAAQ,OAAO;AAAA,MAC9D,2BAA2B;AAAA,QACzB;AAAA,MACF;AAAA,MACA,KAAK,GAAG,KAAK,OAAO,OAAO;AAAA,IAC7B,CAAC;AAED,UAAM,EAAE,UAAU,WAAW,GAAG,YAAY,IAAI;AAEhD,UAAM,QACJ,QAAQ,KAAK,SAAS,YAClB,QAAQ,KAAK,QACb,QAAQ,KAAK,SAAS,gBACpB,CAAC,QAAQ,KAAK,IAAI,IAClB;AAER,UAAM,2BAA2B,IAAI,yBAAyB;AAAA,MAC5D;AAAA,MACA;AAAA,IACF,CAAC;AAED,QAAI,eAA4C;AAChD,QAAI,QAA4D;AAAA,MAC9D,kBAAkB,OAAO;AAAA,MACzB,cAAc,OAAO;AAAA,IACvB;AAEA,UAAM,EAAE,0BAA0B,KAAK,IAAI,KAAK;AAEhD,WAAO;AAAA,MACL,SAAS,EAAE,WAAW,YAAY;AAAA,MAClC,aAAa,EAAE,SAAS,gBAAgB;AAAA,MACxC,SAAS,EAAE,MAAM,KAAK,UAAU,IAAI,EAAE;AAAA,MACtC,QAAQ,SAAS;AAAA,QACf,IAAI,gBAGF;AAAA,UACA,MAAM,MAAM,YAAY;AACtB,uBAAW,QAAQ;AAAA,cACjB;AAAA,cACA,MAAM;AAAA,cACN;AAAA,YACF,CAAC;AAAA,UACH;AAAA,UACA,MAAM,UAAU,OAAO,YAAY;AACjC,gBAAI,CAAC,MAAM,SAAS;AAClB,yBAAW,QAAQ,EAAE,OAAO,MAAM,OAAO,MAAM,QAAQ,CAAC;AACxD;AAAA,YACF;AAEA,kBAAM,QAAQ,MAAM;AAEpB,gBAAI,MAAM,MAAM;AACd,6BAAe,yBAAyB,OAAO,EAAE,WAAW,CAAC;AAC7D,sBAAQ;AAAA,gBACN,kBAAkB,MAAM;AAAA,gBACxB,cAAc,MAAM,qBAAqB;AAAA,cAC3C;AAEA;AAAA,YACF;AAEA,gBAAI,yBAAyB;AAC3B,oBAAM,mBAAmB,yBAAyB,MAAM;AAAA,gBACtD;AAAA,gBACA,OAAO,MAAM,QAAQ;AAAA,cACvB,CAAC;AAED,kBAAI,kBAAkB;AACpB;AAAA,cACF;AAAA,YACF;AAEA,gBAAI,MAAM,QAAQ,YAAY,MAAM;AAClC,yBAAW,QAAQ;AAAA,gBACjB,WAAW,MAAM,QAAQ;AAAA,gBACzB,MAAM;AAAA,cACR,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,2BAA2B,cAAE,OAAO;AAAA,EACxC,YAAY,cAAE,OAAO;AAAA,EACrB,MAAM,cAAE,QAAQ,IAAI;AAAA,EACpB,aAAa,cAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAAA,EAC5C,YAAY,cAAE,OAAO;AAAA,EACrB,eAAe,cAAE,OAAO;AAAA,EACxB,eAAe,cAAE,OAAO,EAAE,SAAS;AAAA,EACnC,SAAS,cAAE,OAAO;AAAA,IAChB,SAAS,cAAE,OAAO;AAAA,IAClB,MAAM,cAAE,OAAO;AAAA,IACf,YAAY,cACT;AAAA,MACC,cAAE,OAAO;AAAA,QACP,UAAU,cAAE,OAAO;AAAA,UACjB,WAAW,cAAE,OAAO,cAAE,IAAI,CAAC;AAAA,UAC3B,MAAM,cAAE,OAAO;AAAA,QACjB,CAAC;AAAA,QACD,IAAI,cAAE,OAAO,EAAE,SAAS;AAAA,MAC1B,CAAC;AAAA,IACH,EACC,SAAS,EACT,SAAS;AAAA,EACd,CAAC;AAAA,EACD,OAAO,cAAE,OAAO;AAAA,EAChB,mBAAmB,cAAE,OAAO,EAAE,SAAS;AAAA,EACvC,sBAAsB,cAAE,OAAO,EAAE,SAAS;AAAA,EAC1C,gBAAgB,cAAE,OAAO;AAC3B,CAAC;AAID,IAAM,8BAA8B,cAAE,mBAAmB,QAAQ;AAAA,EAC/D,cAAE,OAAO;AAAA,IACP,YAAY,cAAE,OAAO;AAAA,IACrB,MAAM,cAAE,QAAQ,KAAK;AAAA,IACrB,SAAS,cAAE,OAAO;AAAA,MAChB,SAAS,cAAE,OAAO;AAAA,MAClB,MAAM,cAAE,OAAO;AAAA,IACjB,CAAC;AAAA,IACD,OAAO,cAAE,OAAO;AAAA,EAClB,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACP,YAAY,cAAE,OAAO;AAAA,IACrB,MAAM,cAAE,QAAQ,IAAI;AAAA,IACpB,YAAY,cAAE,OAAO;AAAA,IACrB,eAAe,cAAE,OAAO;AAAA,IACxB,eAAe,cAAE,OAAO,EAAE,SAAS;AAAA,IACnC,OAAO,cAAE,OAAO;AAAA,IAChB,mBAAmB,cAAE,OAAO,EAAE,SAAS;AAAA,IACvC,sBAAsB,cAAE,OAAO,EAAE,SAAS;AAAA,IAC1C,gBAAgB,cAAE,OAAO;AAAA,EAC3B,CAAC;AACH,CAAC;;;AS3aD,IAAAC,mBAGO;AACP,IAAAC,yBAGO;AACP,IAAAC,cAAkB;AAcX,IAAM,uBAAN,MAA+D;AAAA,EAmBpE,YACE,SACA,UACA,QACA;AAtBF,SAAS,uBAAuB;AAuB9B,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,SAAS;AAAA,EAChB;AAAA,EApBA,IAAI,WAAmB;AACrB,WAAO,KAAK,OAAO;AAAA,EACrB;AAAA,EAEA,IAAI,uBAA+B;AAjCrC;AAkCI,YAAO,UAAK,SAAS,yBAAd,YAAsC;AAAA,EAC/C;AAAA,EAEA,IAAI,wBAAiC;AACnC,WAAO;AAAA,EACT;AAAA,EAYA,MAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,EACF,GAEE;AACA,QAAI,OAAO,SAAS,KAAK,sBAAsB;AAC7C,YAAM,IAAI,oDAAmC;AAAA,QAC3C,sBAAsB,KAAK;AAAA,QAC3B,SAAS,KAAK;AAAA,QACd,UAAU,KAAK;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,EAAE,iBAAiB,OAAO,SAAS,IAAI,UAAM,sCAAc;AAAA,MAC/D;AAAA,MACA,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,OAAO,KAAK;AAAA,MACd;AAAA,MACA,uBAAuB;AAAA,MACvB,OAAO,KAAK,OAAO;AAAA,MACnB,SAAS,KAAK,OAAO,QAAQ;AAAA,MAC7B,+BAA2B;AAAA,QACzB;AAAA,MACF;AAAA,MACA,KAAK,GAAG,KAAK,OAAO,OAAO;AAAA,IAC7B,CAAC;AAED,WAAO;AAAA,MACL,YAAY,SAAS;AAAA,MACrB,aAAa,EAAE,SAAS,gBAAgB;AAAA,MACxC,OAAO,SAAS,oBACZ,EAAE,QAAQ,SAAS,kBAAkB,IACrC;AAAA,IACN;AAAA,EACF;AACF;AAEA,IAAM,oCAAoC,cAAE,OAAO;AAAA,EACjD,YAAY,cAAE,MAAM,cAAE,MAAM,cAAE,OAAO,CAAC,CAAC;AAAA,EACvC,mBAAmB,cAAE,OAAO,EAAE,SAAS;AACzC,CAAC;;;AVvCM,SAAS,aACd,UAAkC,CAAC,GACnB;AAzDlB;AA0DE,QAAM,WACJ,sDAAqB,QAAQ,OAAO,MAApC,YAAyC;AAE3C,QAAM,aAAa,OAAO;AAAA,IACxB,GAAG,QAAQ;AAAA,EACb;AAEA,QAAM,kBAAkB,CACtB,SACA,WAA+B,CAAC,MAEhC,IAAI,wBAAwB,SAAS,UAAU;AAAA,IAC7C;AAAA,IACA,OAAO,QAAQ;AAAA,IACf,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC;AAEH,QAAM,uBAAuB,CAC3B,SACA,WAAoC,CAAC,MAErC,IAAI,qBAAqB,SAAS,UAAU;AAAA,IAC1C;AAAA,IACA,OAAO,QAAQ;AAAA,IACf,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC;AAEH,QAAM,WAAW,SACf,SACA,UACA;AACA,QAAI,YAAY;AACd,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO,gBAAgB,SAAS,QAAQ;AAAA,EAC1C;AAEA,WAAS,OAAO;AAChB,WAAS,YAAY;AACrB,WAAS,gBAAgB;AACzB,WAAS,gBAAgB;AACzB,WAAS,qBAAqB;AAE9B,SAAO;AACT;AAEO,IAAM,SAAS,aAAa;", "names": ["import_provider_utils", "import_provider_utils", "import_zod", "import_provider_utils", "import_provider_utils", "import_provider", "import_provider", "import_provider_utils", "_a", "import_provider", "import_provider_utils", "import_zod"]}