{"name": "partial-json", "version": "0.1.7", "description": "Parse partial JSON generated by LLM", "keywords": ["JSON", "parser", "LLM", "nlp"], "homepage": "https://promplate.dev/partial-json-parser", "repository": "https://github.com/promplate/partial-json-parser-js", "license": "MIT", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./options": {"types": "./dist/options.d.ts", "default": "./dist/options.js"}}, "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsc", "coverage": "vitest run --coverage", "test": "vitest", "test:ui": "vitest --ui"}, "devDependencies": {"@vitest/coverage-istanbul": "^1.2.2", "@vitest/ui": "^1.2.2", "typescript": "^5.3.3", "vitest": "^1.2.2"}}