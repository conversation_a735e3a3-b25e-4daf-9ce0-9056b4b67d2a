{"name": "throttleit", "version": "2.1.0", "description": "Throttle a function to limit its execution rate", "license": "MIT", "repository": "sindresorhus/throttleit", "funding": "https://github.com/sponsors/sindresorhus", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "main": "./index.js", "types": "./index.d.ts", "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava"}, "files": ["index.js", "index.d.ts"], "keywords": ["throttle", "rate", "limit", "limited", "rate-limit", "ratelimit", "throttling", "optimization", "performance", "function", "execution", "interval", "batch"], "devDependencies": {"ava": "^5.3.1", "xo": "^0.56.0"}, "xo": {"rules": {"unicorn/prefer-module": "off"}}}