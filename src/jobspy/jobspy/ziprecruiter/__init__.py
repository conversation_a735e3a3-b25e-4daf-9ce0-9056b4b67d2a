from __future__ import annotations

import json
import math
import re
import time
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from datetime import datetime

from bs4 import BeautifulSoup

from jobspy.ziprecruiter.constant import headers, get_cookie_data
from jobspy.util import (
    extract_emails_from_text,
    create_session,
    markdown_converter,
    remove_attributes,
    create_logger,
)
from jobspy.enhanced_session import create_enhanced_session
from jobspy.model import (
    JobPost,
    Compensation,
    Location,
    JobResponse,
    Country,
    DescriptionFormat,
    Scraper,
    ScraperInput,
    Site,
)
from jobspy.ziprecruiter.util import get_job_type_enum, add_params

log = create_logger("ZipRecruiter")


class ZipRecruiter(Scraper):
    base_url = "https://www.ziprecruiter.com"
    api_url = "https://api.ziprecruiter.com"

    def __init__(
        self, proxies: list[str] | str | None = None, ca_cert: str | None = None
    ):
        """
        Initializes ZipRecruiterScraper with the ZipR<PERSON>ruiter job search url
        """
        super().__init__(Site.ZIP_RECRUITER, proxies=proxies)

        self.scraper_input = None
        # Use enhanced session with anti-detection
        self.session = create_enhanced_session("ziprecruiter", proxies=proxies)
        self._get_cookies()

        self.base_delay = 8  # Increased base delay
        self.max_delay = 20  # Maximum delay for exponential backoff
        self.jobs_per_page = 20
        self.seen_urls = set()
        self.request_count = 0
        self.last_request_time = 0

    def scrape(self, scraper_input: ScraperInput) -> JobResponse:
        """
        Scrapes ZipRecruiter for jobs with scraper_input criteria.
        :param scraper_input: Information about job search criteria.
        :return: JobResponse containing a list of jobs.
        """
        self.scraper_input = scraper_input
        job_list: list[JobPost] = []
        continue_token = None

        max_pages = math.ceil(scraper_input.results_wanted / self.jobs_per_page)
        for page in range(1, max_pages + 1):
            if len(job_list) >= scraper_input.results_wanted:
                break
            if page > 1:
                delay = self._get_adaptive_delay()
                log.info(f"Waiting {delay:.2f} seconds before next request...")
                time.sleep(delay)
            log.info(f"search page: {page} / {max_pages}")
            jobs_on_page, continue_token = self._find_jobs_in_page(
                scraper_input, continue_token
            )
            if jobs_on_page:
                job_list.extend(jobs_on_page)
            else:
                break
            if not continue_token:
                break
        return JobResponse(jobs=job_list[: scraper_input.results_wanted])

    def _get_adaptive_delay(self) -> float:
        """Calculate adaptive delay based on request count and timing"""
        import random

        # Base delay with jitter
        base_delay = self.base_delay + random.uniform(1, 4)

        # Increase delay based on request count (burst protection)
        if self.request_count > 5:
            base_delay *= 1.5
        if self.request_count > 10:
            base_delay *= 2

        # Add extra delay if requests are too frequent
        current_time = time.time()
        if self.last_request_time > 0:
            time_since_last = current_time - self.last_request_time
            if time_since_last < 5:  # Less than 5 seconds since last request
                base_delay *= 1.8

        return min(base_delay, self.max_delay)

    def _find_jobs_in_page(
        self, scraper_input: ScraperInput, continue_token: str | None = None
    ) -> tuple[list[JobPost], str | None]:
        """
        Scrapes a page of ZipRecruiter for jobs with scraper_input criteria
        :param scraper_input:
        :param continue_token:
        :return: jobs found on page
        """
        jobs_list = []
        params = add_params(scraper_input)
        if continue_token:
            params["continue_from"] = continue_token
        # Implement retry logic with exponential backoff
        max_retries = 3
        retry_count = 0

        while retry_count <= max_retries:
            try:
                # Update request tracking
                self.request_count += 1
                self.last_request_time = time.time()

                res = self.session.get(f"{self.api_url}/jobs-app/jobs", params=params)

                if res.status_code == 429:
                    retry_count += 1
                    if retry_count <= max_retries:
                        # Exponential backoff: 30s, 60s, 120s
                        backoff_delay = 30 * (2 ** (retry_count - 1))
                        log.warning(f"Rate limited (429). Retry {retry_count}/{max_retries} after {backoff_delay}s...")
                        time.sleep(backoff_delay)
                        continue
                    else:
                        err = "429 Response - Blocked by ZipRecruiter for too many requests (max retries exceeded)"
                        log.error(err)
                        return jobs_list, ""

                elif res.status_code not in range(200, 400):
                    err = f"ZipRecruiter response status code {res.status_code}"
                    err += f" with response: {res.text}"
                    log.error(err)
                    return jobs_list, ""
                else:
                    # Success - break out of retry loop
                    break

            except Exception as e:
                retry_count += 1
                if "Proxy responded with" in str(e):
                    log.error(f"ZipRecruiter: Bad proxy")
                else:
                    log.error(f"ZipRecruiter: {str(e)}")

                if retry_count <= max_retries:
                    backoff_delay = 15 * retry_count
                    log.warning(f"Request failed. Retry {retry_count}/{max_retries} after {backoff_delay}s...")
                    time.sleep(backoff_delay)
                    continue
                else:
                    return jobs_list, ""

        res_data = res.json()
        jobs_list = res_data.get("jobs", [])
        next_continue_token = res_data.get("continue", None)
        with ThreadPoolExecutor(max_workers=self.jobs_per_page) as executor:
            job_results = [executor.submit(self._process_job, job) for job in jobs_list]

        job_list = list(filter(None, (result.result() for result in job_results)))
        return job_list, next_continue_token

    def _process_job(self, job: dict) -> JobPost | None:
        """
        Processes an individual job dict from the response
        """
        title = job.get("name")
        job_url = f"{self.base_url}/jobs//j?lvk={job['listing_key']}"
        if job_url in self.seen_urls:
            return
        self.seen_urls.add(job_url)

        description = job.get("job_description", "").strip()
        listing_type = job.get("buyer_type", "")
        description = (
            markdown_converter(description)
            if self.scraper_input.description_format == DescriptionFormat.MARKDOWN
            else description
        )
        company = job.get("hiring_company", {}).get("name")
        country_value = "usa" if job.get("job_country") == "US" else "canada"
        country_enum = Country.from_string(country_value)

        location = Location(
            city=job.get("job_city"), state=job.get("job_state"), country=country_enum
        )
        job_type = get_job_type_enum(
            job.get("employment_type", "").replace("_", "").lower()
        )
        date_posted = datetime.fromisoformat(job["posted_time"].rstrip("Z")).date()
        comp_interval = job.get("compensation_interval")
        comp_interval = "yearly" if comp_interval == "annual" else comp_interval
        comp_min = int(job["compensation_min"]) if "compensation_min" in job else None
        comp_max = int(job["compensation_max"]) if "compensation_max" in job else None
        comp_currency = job.get("compensation_currency")
        description_full, job_url_direct = self._get_descr(job_url)

        return JobPost(
            id=f'zr-{job["listing_key"]}',
            title=title,
            company_name=company,
            location=location,
            job_type=job_type,
            compensation=Compensation(
                interval=comp_interval,
                min_amount=comp_min,
                max_amount=comp_max,
                currency=comp_currency,
            ),
            date_posted=date_posted,
            job_url=job_url,
            description=description_full if description_full else description,
            emails=extract_emails_from_text(description) if description else None,
            job_url_direct=job_url_direct,
            listing_type=listing_type,
        )

    def _get_descr(self, job_url):
        res = self.session.get(job_url, allow_redirects=True)
        description_full = job_url_direct = None
        if res.ok:
            soup = BeautifulSoup(res.text, "html.parser")
            job_descr_div = soup.find("div", class_="job_description")
            company_descr_section = soup.find("section", class_="company_description")
            job_description_clean = (
                remove_attributes(job_descr_div).prettify(formatter="html")
                if job_descr_div
                else ""
            )
            company_description_clean = (
                remove_attributes(company_descr_section).prettify(formatter="html")
                if company_descr_section
                else ""
            )
            description_full = job_description_clean + company_description_clean

            try:
                script_tag = soup.find("script", type="application/json")
                if script_tag:
                    job_json = json.loads(script_tag.string)
                    job_url_val = job_json["model"].get("saveJobURL", "")
                    m = re.search(r"job_url=(.+)", job_url_val)
                    if m:
                        job_url_direct = m.group(1)
            except:
                job_url_direct = None

            if self.scraper_input.description_format == DescriptionFormat.MARKDOWN:
                description_full = markdown_converter(description_full)

        return description_full, job_url_direct

    def _get_cookies(self):
        """
        Sends a session event to the API with device properties and enhanced fingerprinting.
        """
        import random

        # Generate dynamic device properties
        device_data = list(get_cookie_data)

        # Randomize some properties
        device_data = [
            (k, v) if k != "property" else (k, self._randomize_property(v))
            for k, v in device_data
        ]

        try:
            url = f"{self.api_url}/jobs-app/event"
            response = self.session.post(url, data=device_data, timeout=10)
            if response.status_code == 200:
                log.info("Successfully initialized ZipRecruiter session")
            else:
                log.warning(f"Session initialization returned {response.status_code}")
        except Exception as e:
            log.error(f"Failed to initialize session: {str(e)}")

    def _randomize_property(self, prop_value: str) -> str:
        """Randomize device properties to avoid fingerprinting"""
        import random

        if "timestamp:" in prop_value:
            from datetime import datetime, timedelta
            # Use recent timestamp with some variance
            now = datetime.now() - timedelta(minutes=random.randint(1, 30))
            return f"timestamp:{now.isoformat()}"
        elif "screen_height:" in prop_value:
            heights = ["852", "926", "844", "812", "736", "932", "956"]
            return f"screen_height:{random.choice(heights)}"
        elif "screen_width:" in prop_value:
            widths = ["393", "428", "390", "375", "414", "430"]
            return f"screen_width:{random.choice(widths)}"
        elif "device_model:" in prop_value:
            models = ["iPhone 14 Pro", "iPhone 15", "iPhone 13", "iPhone 14", "iPhone 15 Pro", "iPhone 13 Pro", "iPhone 14 Plus"]
            return f"device_model:{random.choice(models)}"
        elif "app_build_number:" in prop_value:
            builds = ["4734", "4735", "4736", "4737", "4738", "4739", "4740"]
            return f"app_build_number:{random.choice(builds)}"
        elif "app_version:" in prop_value:
            versions = ["91.0", "91.1", "91.2", "92.0", "92.1", "90.9"]
            return f"app_version:{random.choice(versions)}"
        elif "os_version:" in prop_value:
            versions = ["16.6.1", "17.0.1", "17.1.2", "17.2.1", "17.3.1", "17.4.1"]
            return f"os_version:{random.choice(versions)}"

        return prop_value
