from __future__ import annotations

import random
import time

from bs4 import BeautifulSoup

from jobspy.model import (
    <PERSON><PERSON><PERSON>,
    ScraperInput,
    Site,
    JobPost,
    JobResponse,
    Location,
    Country,
)
from jobspy.util import create_logger, create_session
from jobspy.enhanced_session import create_enhanced_session

log = create_logger("Bayt")


class BaytScraper(Scraper):
    base_url = "https://www.bayt.com"
    delay = 3  # Increased base delay
    band_delay = 5  # Increased random delay range

    def __init__(
        self, proxies: list[str] | str | None = None, ca_cert: str | None = None
    ):
        super().__init__(Site.BAYT, proxies=proxies, ca_cert=ca_cert)
        self.scraper_input = None
        self.session = None
        self.country = "worldwide"
        self.request_count = 0
        self.session_initialized = False

    def scrape(self, scraper_input: ScraperInput) -> JobResponse:
        self.scraper_input = scraper_input
        # Use enhanced session with anti-detection
        self.session = create_enhanced_session("bayt", proxies=self.proxies)

        # Initialize session with proper headers and warm-up
        self._initialize_session()

        job_list: list[JobPost] = []
        page = 1
        results_wanted = (
            scraper_input.results_wanted if scraper_input.results_wanted else 10
        )

        while len(job_list) < results_wanted:
            log.info(f"Fetching Bayt jobs page {page}")
            job_elements = self._fetch_jobs(self.scraper_input.search_term, page)
            if not job_elements:
                break

            if job_elements:
                log.debug(
                    "First job element snippet:\n" + job_elements[0].prettify()[:500]
                )

            initial_count = len(job_list)
            for job in job_elements:
                try:
                    job_post = self._extract_job_info(job)
                    if job_post:
                        job_list.append(job_post)
                        if len(job_list) >= results_wanted:
                            break
                    else:
                        log.debug(
                            "Extraction returned None. Job snippet:\n"
                            + job.prettify()[:500]
                        )
                except Exception as e:
                    log.error(f"Bayt: Error extracting job info: {str(e)}")
                    continue

            if len(job_list) == initial_count:
                log.info(f"No new jobs found on page {page}. Ending pagination.")
                break

            page += 1
            time.sleep(random.uniform(self.delay, self.delay + self.band_delay))

        job_list = job_list[: scraper_input.results_wanted]
        return JobResponse(jobs=job_list)

    def _initialize_session(self):
        """Initialize session with proper headers and warm-up request"""
        if self.session_initialized:
            return

        try:
            # Add Bayt-specific headers
            self.session.headers.update({
                "authority": "www.bayt.com",
                "origin": "https://www.bayt.com",
                "referer": "https://www.bayt.com/",
                "sec-fetch-dest": "document",
                "sec-fetch-mode": "navigate",
                "sec-fetch-site": "same-origin",
                "sec-fetch-user": "?1",
                "upgrade-insecure-requests": "1",
                "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
            })

            # Warm-up request to establish session
            log.info("Warming up Bayt session...")
            response = self.session.get(self.base_url, timeout=15)
            if response.status_code == 200:
                log.info("Bayt session initialized successfully")
                self.session_initialized = True
                # Add a delay after session initialization
                time.sleep(random.uniform(2, 4))
            else:
                log.warning(f"Session warm-up returned status {response.status_code}")

        except Exception as e:
            log.error(f"Failed to initialize Bayt session: {str(e)}")
            # Continue anyway, but with a longer delay
            time.sleep(random.uniform(5, 8))

    def _fetch_jobs(self, query: str, page: int) -> list | None:
        """
        Grabs the job results for the given query and page number.
        """
        max_retries = 3
        retry_count = 0

        while retry_count <= max_retries:
            try:
                # Add request tracking and adaptive delay
                self.request_count += 1
                if self.request_count > 1:
                    delay = self._get_adaptive_delay()
                    log.info(f"Waiting {delay:.2f} seconds before Bayt request...")
                    time.sleep(delay)

                # Use a more natural URL structure
                url = f"{self.base_url}/en/international/jobs/{query.replace(' ', '%20')}-jobs/"
                if page > 1:
                    url += f"?page={page}"

                log.info(f"Fetching Bayt URL: {url}")
                response = self.session.get(url, timeout=20)

                if response.status_code == 403:
                    retry_count += 1
                    if retry_count <= max_retries:
                        backoff_delay = 10 * (2 ** (retry_count - 1))  # 10s, 20s, 40s
                        log.warning(f"403 Forbidden. Retry {retry_count}/{max_retries} after {backoff_delay}s...")
                        time.sleep(backoff_delay)
                        # Refresh session on 403
                        self._refresh_session()
                        continue
                    else:
                        log.error("Max retries exceeded for 403 errors")
                        return None

                response.raise_for_status()
                soup = BeautifulSoup(response.text, "html.parser")
                job_listings = soup.find_all("li", attrs={"data-js-job": ""})
                log.debug(f"Found {len(job_listings)} job listing elements")
                return job_listings

            except Exception as e:
                retry_count += 1
                log.error(f"Bayt: Error fetching jobs - {str(e)}")
                if retry_count <= max_retries:
                    backoff_delay = 5 * retry_count
                    log.warning(f"Request failed. Retry {retry_count}/{max_retries} after {backoff_delay}s...")
                    time.sleep(backoff_delay)
                    continue
                else:
                    return None

        return None

    def _get_adaptive_delay(self) -> float:
        """Calculate adaptive delay based on request count"""
        base_delay = self.delay + random.uniform(0, self.band_delay)

        # Increase delay based on request count
        if self.request_count > 3:
            base_delay *= 1.5
        if self.request_count > 6:
            base_delay *= 2

        return min(base_delay, 15)  # Cap at 15 seconds

    def _refresh_session(self):
        """Refresh session headers and cookies on 403 errors"""
        try:
            log.info("Refreshing Bayt session due to 403 error...")

            # Clear cookies
            self.session.cookies.clear()

            # Update headers with new user agent
            from jobspy.anti_detection import anti_detection
            new_headers = anti_detection.get_realistic_headers("bayt")
            self.session.headers.update(new_headers)

            # Re-add Bayt-specific headers
            self.session.headers.update({
                "authority": "www.bayt.com",
                "origin": "https://www.bayt.com",
                "referer": "https://www.bayt.com/",
            })

            # Reset session initialization flag
            self.session_initialized = False

            # Add extra delay
            time.sleep(random.uniform(3, 6))

        except Exception as e:
            log.error(f"Failed to refresh session: {str(e)}")

    def _extract_job_info(self, job: BeautifulSoup) -> JobPost | None:
        """
        Extracts the job information from a single job listing.
        """
        # Find the h2 element holding the title and link (no class filtering)
        job_general_information = job.find("h2")
        if not job_general_information:
            return

        job_title = job_general_information.get_text(strip=True)
        job_url = self._extract_job_url(job_general_information)
        if not job_url:
            return

        # Extract company name using the original approach:
        company_tag = job.find("div", class_="t-nowrap p10l")
        company_name = (
            company_tag.find("span").get_text(strip=True)
            if company_tag and company_tag.find("span")
            else None
        )

        # Extract location using the original approach:
        location_tag = job.find("div", class_="t-mute t-small")
        location = location_tag.get_text(strip=True) if location_tag else None

        job_id = f"bayt-{abs(hash(job_url))}"
        location_obj = Location(
            city=location,
            country=Country.from_string(self.country),
        )
        return JobPost(
            id=job_id,
            title=job_title,
            company_name=company_name,
            location=location_obj,
            job_url=job_url,
        )

    def _extract_job_url(self, job_general_information: BeautifulSoup) -> str | None:
        """
        Pulls the job URL from the 'a' within the h2 element.
        """
        a_tag = job_general_information.find("a")
        if a_tag and a_tag.has_attr("href"):
            return self.base_url + a_tag["href"].strip()
