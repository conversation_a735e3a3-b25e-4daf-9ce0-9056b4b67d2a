"""
Enhanced Session Management for JobSpy
Provides advanced session handling with anti-detection measures
"""

import random
import time
import json
import os
import pickle
from typing import Dict, Optional, Any
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from .anti_detection import anti_detection, get_throttler


class EnhancedSession(requests.Session):
    """Enhanced session with anti-detection and intelligent retry logic"""
    
    def __init__(self, site: str = "generic", proxies: Optional[list] = None,
                 persist_cookies: bool = True):
        super().__init__()
        self.site = site
        self.proxies_list = proxies or []
        self.current_proxy_index = 0
        self.failed_proxies = set()
        self.session_fingerprint = anti_detection.get_session_fingerprint()
        self.throttler = get_throttler(site)
        self.persist_cookies = persist_cookies
        self.cookie_file = f"/tmp/jobspy_cookies_{site}.pkl"

        # Setup session with realistic headers
        self._setup_session()
        self._setup_retries()

        # Load persisted cookies
        if persist_cookies:
            self._load_cookies()
        
    def _setup_session(self):
        """Setup session with realistic headers and settings"""
        headers = anti_detection.get_realistic_headers(self.site)
        self.headers.update(headers)
        
        # Add session-specific headers
        self.headers.update({
            "Cache-Control": "no-cache",
            "Pragma": "no-cache",
            "Sec-GPC": "1",
        })
        
        # Set realistic timeout
        self.timeout = (10, 30)  # (connect, read)
        
    def _setup_retries(self):
        """Setup intelligent retry strategy"""
        retry_strategy = Retry(
            total=5,
            status_forcelist=[429, 500, 502, 503, 504, 403],
            backoff_factor=2,
            respect_retry_after_header=True,
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.mount("http://", adapter)
        self.mount("https://", adapter)
        
    def _rotate_proxy(self):
        """Rotate to next available proxy"""
        if not self.proxies_list:
            return
            
        available_proxies = [p for i, p in enumerate(self.proxies_list) 
                           if i not in self.failed_proxies]
        
        if not available_proxies:
            # Reset failed proxies if all are failed
            self.failed_proxies.clear()
            available_proxies = self.proxies_list
            
        if available_proxies:
            proxy = random.choice(available_proxies)
            self.proxies = {
                "http": proxy,
                "https": proxy
            }
            
    def _refresh_headers(self):
        """Refresh headers with new user agent and fingerprint"""
        new_headers = anti_detection.get_realistic_headers(self.site)
        self.headers.update(new_headers)
        self.session_fingerprint = anti_detection.get_session_fingerprint()
        
    def request(self, method: str, url: str, **kwargs) -> requests.Response:
        """Enhanced request method with anti-detection measures"""

        # Check if we should throttle
        if self.throttler.should_throttle():
            delay = self.throttler.get_burst_delay()
            print(f"Throttling {self.site} request for {delay:.2f}s")
            time.sleep(delay)
        else:
            # Normal delay between requests
            delay = self.throttler.get_delay()
            time.sleep(delay)
        
        # Rotate proxy if needed
        if self.proxies_list and random.random() < 0.1:  # 10% chance to rotate
            self._rotate_proxy()
            
        # Refresh headers occasionally
        if random.random() < 0.05:  # 5% chance to refresh
            self._refresh_headers()
            
        # Add realistic request modifications
        kwargs.setdefault('timeout', self.timeout)
        kwargs.setdefault('allow_redirects', True)
        
        try:
            response = super().request(method, url, **kwargs)

            # Record request with status code
            self.throttler.record_request(
                success=response.status_code < 400,
                status_code=response.status_code
            )

            # Handle specific error codes
            if response.status_code == 403:
                self._handle_403_error(url)
            elif response.status_code == 429:
                self._handle_rate_limit(response)

            return response

        except Exception as e:
            # Record failed request
            self.throttler.record_request(success=False, status_code=500)

            # Mark current proxy as failed if using proxies
            if self.proxies_list and hasattr(self, 'current_proxy_index'):
                self.failed_proxies.add(self.current_proxy_index)

            raise e
    
    def _handle_403_error(self, url: str):
        """Handle 403 Forbidden errors"""
        print(f"403 Forbidden for {url} - rotating session...")
        
        # Rotate proxy
        self._rotate_proxy()
        
        # Refresh headers completely
        self._refresh_headers()
        
        # Clear cookies
        self.cookies.clear()
        
        # Add longer delay
        time.sleep(random.uniform(5, 10))

    def _load_cookies(self):
        """Load cookies from persistent storage"""
        try:
            if os.path.exists(self.cookie_file):
                with open(self.cookie_file, 'rb') as f:
                    cookies = pickle.load(f)
                    self.cookies.update(cookies)
                print(f"Loaded {len(cookies)} cookies for {self.site}")
        except Exception as e:
            print(f"Failed to load cookies for {self.site}: {str(e)}")

    def _save_cookies(self):
        """Save cookies to persistent storage"""
        try:
            if self.persist_cookies and self.cookies:
                os.makedirs(os.path.dirname(self.cookie_file), exist_ok=True)
                with open(self.cookie_file, 'wb') as f:
                    pickle.dump(dict(self.cookies), f)
        except Exception as e:
            print(f"Failed to save cookies for {self.site}: {str(e)}")

    def close(self):
        """Close session and save cookies"""
        if self.persist_cookies:
            self._save_cookies()
        super().close()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
        
    def _handle_rate_limit(self, response: requests.Response):
        """Handle rate limiting"""
        retry_after = response.headers.get('Retry-After')
        if retry_after:
            try:
                delay = int(retry_after)
                print(f"Rate limited - waiting {delay} seconds...")
                time.sleep(delay)
            except ValueError:
                time.sleep(60)  # Default 1 minute
        else:
            time.sleep(random.uniform(30, 60))


class SiteSpecificSession:
    """Factory for creating site-specific enhanced sessions"""
    
    @staticmethod
    def create_glassdoor_session(proxies: Optional[list] = None) -> EnhancedSession:
        """Create optimized session for Glassdoor"""
        session = EnhancedSession("glassdoor", proxies)
        
        # Glassdoor-specific headers
        session.headers.update({
            "apollographql-client-name": "job-search-next",
            "apollographql-client-version": "4.65.5",
            "content-type": "application/json",
        })
        
        return session
    
    @staticmethod
    def create_ziprecruiter_session(proxies: Optional[list] = None) -> EnhancedSession:
        """Create optimized session for ZipRecruiter"""
        session = EnhancedSession("ziprecruiter", proxies)

        # Generate more dynamic device identifiers
        device_id = f"D{random.randint(10, 99)}{random.choice(['A', 'B', 'C', 'D'])}{random.randint(1000, 9999)}-E{random.randint(100, 999)}-{random.randint(10, 99)}{random.choice(['A', 'B', 'C', 'D'])}{random.randint(1, 9)}-{random.randint(1000, 9999)}-{random.randint(100000000000, 999999999999)}"
        push_id = f"{random.randint(0, 9)}{random.choice(['a', 'b', 'c', 'd', 'e', 'f'])}{random.randint(10, 99)}{random.choice(['a', 'b', 'c', 'd', 'e', 'f'])}{random.randint(1000, 9999)}{random.choice(['a', 'b', 'c', 'd', 'e', 'f'])}{random.randint(10000, 99999)}{random.choice(['a', 'b', 'c', 'd', 'e', 'f'])}{random.randint(100000, 999999)}{random.choice(['a', 'b', 'c', 'd', 'e', 'f'])}{random.randint(10000000, 99999999)}"

        # ZipRecruiter-specific headers (mobile API simulation)
        session.headers.update({
            "Host": "api.ziprecruiter.com",
            "accept": "*/*",
            "x-zr-zva-override": f"10000000{random.randint(0, 9)};vid:ZT1{random.choice(['a', 'b', 'c', 'd', 'e', 'f'])}{random.randint(100, 999)}_EQlDTVEc",
            "x-pushnotificationid": push_id,
            "x-deviceid": device_id,
            "authorization": "Basic YTBlZjMyZDYtN2I0Yy00MWVkLWEyODMtYTI1NDAzMzI0YTcyOg==",
        })

        return session

    @staticmethod
    def create_bayt_session(proxies: Optional[list] = None) -> EnhancedSession:
        """Create optimized session for Bayt"""
        session = EnhancedSession("bayt", proxies)

        # Bayt-specific headers
        session.headers.update({
            "authority": "www.bayt.com",
            "origin": "https://www.bayt.com",
            "referer": "https://www.bayt.com/",
            "sec-fetch-dest": "document",
            "sec-fetch-mode": "navigate",
            "sec-fetch-site": "same-origin",
            "sec-fetch-user": "?1",
            "upgrade-insecure-requests": "1",
            "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
            "accept-language": "en-US,en;q=0.9,ar;q=0.8",  # Include Arabic for Middle East region
        })

        return session
    
    @staticmethod
    def create_bayt_session(proxies: Optional[list] = None) -> EnhancedSession:
        """Create optimized session for Bayt"""
        session = EnhancedSession("bayt", proxies)
        
        # Bayt-specific optimizations
        session.headers.update({
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5,ar;q=0.3",  # Add Arabic for Middle East
        })
        
        return session


class SessionPool:
    """Pool of sessions for load balancing and failover"""
    
    def __init__(self, site: str, pool_size: int = 3, proxies: Optional[list] = None):
        self.site = site
        self.pool_size = pool_size
        self.proxies = proxies or []
        self.sessions = []
        self.current_session_index = 0
        
        self._create_session_pool()
        
    def _create_session_pool(self):
        """Create pool of sessions"""
        factory_map = {
            "glassdoor": SiteSpecificSession.create_glassdoor_session,
            "ziprecruiter": SiteSpecificSession.create_ziprecruiter_session,
            "bayt": SiteSpecificSession.create_bayt_session,
        }
        
        factory = factory_map.get(self.site, lambda p: EnhancedSession(self.site, p))
        
        for i in range(self.pool_size):
            # Distribute proxies across sessions
            session_proxies = self.proxies[i::self.pool_size] if self.proxies else None
            session = factory(session_proxies)
            self.sessions.append(session)
            
    def get_session(self) -> EnhancedSession:
        """Get next session from pool"""
        session = self.sessions[self.current_session_index]
        self.current_session_index = (self.current_session_index + 1) % self.pool_size
        return session
    
    def refresh_pool(self):
        """Refresh all sessions in pool"""
        for session in self.sessions:
            session._refresh_headers()
            session.cookies.clear()


def create_enhanced_session(site: str = "generic", proxies: Optional[list] = None, 
                          use_pool: bool = False) -> EnhancedSession:
    """Factory function to create enhanced sessions"""
    if use_pool:
        pool = SessionPool(site, proxies=proxies)
        return pool.get_session()
    
    factory_map = {
        "glassdoor": SiteSpecificSession.create_glassdoor_session,
        "ziprecruiter": SiteSpecificSession.create_ziprecruiter_session,
        "bayt": SiteSpecificSession.create_bayt_session,
    }
    
    factory = factory_map.get(site, lambda p: EnhancedSession(site, p))
    return factory(proxies)
