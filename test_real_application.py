#!/usr/bin/env python3
"""
Test Real Job Application
Uses a real job posting to test the Stagehand autonomous application flow
"""

import asyncio
import sys
import os
import json
import httpx
from datetime import datetime

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

async def test_job_application():
    """
    Test job application with a real job posting
    """
    
    print("🚀 TESTING REAL JOB APPLICATION")
    print("=" * 50)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Use a real job posting URL (AngelList/Wellfound is good for testing)
    job_data = {
        "title": "Legal Counsel",
        "company": "Test Company",
        "url": "https://angel.co/company/test-company/jobs",  # Generic job board URL
        "description": "Legal counsel position"
    }
    
    # <PERSON><PERSON><PERSON>'s application data
    application_data = {
        "jobId": "test_real_001",
        "name": "<PERSON><PERSON><PERSON>",
        "email": "<EMAIL>",
        "phone": "(*************",
        "location": "Delhi, India",
        "background": "Legal professional with LL.M. in Criminal Law, extensive experience at Delhi High Court and Supreme Court Bar Association"
    }
    
    print(f"👤 Testing with: {application_data['name']}")
    print(f"📧 Email: {application_data['email']}")
    print(f"🎯 Target: {job_data['title']} at {job_data['company']}")
    print(f"🔗 URL: {job_data['url']}")
    print()
    
    # Check service health first
    try:
        async with httpx.AsyncClient() as client:
            health_response = await client.get("http://localhost:3001/health")
            if health_response.status_code == 200:
                print("✅ Stagehand service is healthy")
            else:
                print(f"❌ Stagehand service not healthy: {health_response.status_code}")
                return
    except Exception as e:
        print(f"❌ Cannot connect to Stagehand service: {str(e)}")
        return
    
    # Send application request
    payload = {
        "jobData": job_data,
        "applicationData": application_data
    }
    
    print("🤖 Sending application request to Stagehand...")
    print("⏳ This may take several minutes as the AI agent works...")
    print()
    
    try:
        async with httpx.AsyncClient(timeout=600.0) as client:  # 10 minute timeout
            response = await client.post("http://localhost:3001/apply", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                
                print("📊 APPLICATION RESULT:")
                print(f"✅ Success: {result['success']}")
                print(f"📝 Message: {result['message']}")
                print(f"✔️ Completed: {result['completed']}")
                
                if result.get('screenshots'):
                    print(f"📸 Screenshots: {len(result['screenshots'])}")
                
                if result.get('sessionInfo'):
                    session_info = result['sessionInfo']
                    print(f"🔗 Session ID: {session_info.get('sessionId', 'N/A')}")
                    if session_info.get('debugUrl'):
                        print(f"🐛 Debug URL: {session_info['debugUrl']}")
                
                if result.get('agentResult'):
                    agent_result = result['agentResult']
                    print(f"🤖 Agent Result: {agent_result}")
                
                return result
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"Response: {response.text}")
                return None
                
    except Exception as e:
        print(f"❌ Request failed: {str(e)}")
        return None

async def test_simple_navigation():
    """
    Test simple navigation to a job site
    """
    
    print("\n🌐 TESTING SIMPLE NAVIGATION")
    print("=" * 40)
    
    # Test with a simple job site navigation
    job_data = {
        "title": "Test Navigation",
        "company": "Job Site",
        "url": "https://www.glassdoor.com/Job/index.htm",
        "description": "Test navigation to job site"
    }
    
    application_data = {
        "jobId": "nav_test_001",
        "name": "Sakshi Shah",
        "email": "<EMAIL>",
        "phone": "(*************",
        "location": "Delhi, India",
        "background": "Legal professional"
    }
    
    payload = {
        "jobData": job_data,
        "applicationData": application_data
    }
    
    print(f"🔗 Testing navigation to: {job_data['url']}")
    
    try:
        async with httpx.AsyncClient(timeout=300.0) as client:  # 5 minute timeout
            response = await client.post("http://localhost:3001/apply", json=payload)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Navigation test result: {result['success']}")
                print(f"📝 Message: {result['message']}")
                
                if result.get('sessionInfo', {}).get('debugUrl'):
                    print(f"🐛 Debug URL: {result['sessionInfo']['debugUrl']}")
                
                return result
            else:
                print(f"❌ Navigation test failed: {response.status_code}")
                return None
                
    except Exception as e:
        print(f"❌ Navigation test error: {str(e)}")
        return None

if __name__ == "__main__":
    print("🎬 Starting Real Job Application Test")
    print("This tests the Stagehand autonomous agent with real job sites")
    print()
    
    # Run navigation test first
    nav_result = asyncio.run(test_simple_navigation())
    
    if nav_result and nav_result.get('success'):
        print("\n" + "="*50)
        # Run full application test
        app_result = asyncio.run(test_job_application())
    else:
        print("❌ Navigation test failed, skipping full application test")
    
    print(f"\n🏁 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
